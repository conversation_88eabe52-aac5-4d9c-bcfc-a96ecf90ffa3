using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using System.Resources;

namespace PBC.HelpdeskService.Utilities
{
    /// <summary>
    /// Defines methods for accessing global resources in the HelpdeskService.
    /// </summary>
    public interface IHelpdeskServiceResourceHelper
    {
        /// <summary>
        /// Get global resource object value
        /// </summary>
        /// <param name="cultureValue"></param>
        /// <param name="resourceKey"></param>
        /// <param name="assembly"></param>
        /// <returns></returns>
        IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null);
        /// <summary>
        /// Retrieves a global resource string.
        /// </summary>
        /// <param name="cultureValue"></param>
        /// <param name="resourceKey"></param>
        /// <returns></returns>
        string GetResourceString(string cultureValue, string resourceKey);
    }
    /// <summary>
    /// Provides helper methods for accessing global resources in the HelpdeskService.
    /// </summary>
    public class HelpdeskServiceResourceHelper : IHelpdeskServiceResourceHelper
    {
        private readonly ILogger<HelpdeskServiceResourceHelper> _logger;

        public HelpdeskServiceResourceHelper(ILogger<HelpdeskServiceResourceHelper> logger)
        {
            _logger = logger;
        }
        #region :::GetGlobalResourceObject   Puneeth Y 03-07-2025 :::
        /// <summary>
        /// To Get GlobalResourceObject
        /// </summary>
        /// 
        public IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string resourceValue = resourceManager.GetString(resourceKey);

                return new JsonResult(resourceValue);
            }
            catch (Exception ex)
            {
                // Console.WriteLine($"Error accessing resource: {ex.Message}");
                _logger.LogError(ex, "Error accessing resource: {Message}", ex.Message);
                return new JsonResult(string.Empty);
            }
        }
        public string GetResourceString(string cultureValue, string resourceKey)
        {
            var actionResult = GetGlobalResourceObject(cultureValue, resourceKey);
            if (actionResult is JsonResult jsonResult)
            {
                return jsonResult.Value?.ToString() ?? string.Empty;
            }
            return string.Empty;
        }
        #endregion
    }
}