using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Utilities
{
    public class Common
    {
        public static string DecryptString(string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            str = str.Replace("+", "%Plus%");
            str = Uri.UnescapeDataString(str).Replace("%lthash%", "&#");
            return str.Replace("%Plus%", "+");
        }

        public static string AMP_SP()
        {
            string AMP_SP = System.Configuration.ConfigurationManager.AppSettings.Get("AMP_SP");
            return AMP_SP ?? string.Empty;
        }

        /// <summary>
        /// GetWorkFlowID
        /// </summary>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public static int GetWorkFlowID(string WorkFlowName, string DBName, string connString, int LogException)
        {
            int workflowID = 0;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    SqlCommand command = null;
                    string query = "SELECT WorkFlow_ID FROM GNM_WorkFlow WHERE WorkFlow_Name = @WorkFlowName";

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                if (reader.Read()) // Check if any row is returned
                                {
                                    workflowID = reader.IsDBNull(0) ? 0 : reader.GetInt32(0); // Read the WorkFlow_ID
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                return workflowID;
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                }

                return 0;
            }
        }

        /// <summary>
        /// Generates a DMS password hash using BCrypt with AMP_SP salt
        /// </summary>
        /// <param name="userPassword">Plain text password</param>
        /// <returns>BCrypt hashed password</returns>
        public static string GenerateDMSPassword(string userPassword)
        {
            string ampSp = AMP_SP();
            string pwdToHash = userPassword + ampSp; // PARTSASSIST hard-coded salt
            string hashToStoreInDatabase = BCrypt.HashPassword(pwdToHash, BCrypt.GenerateSalt());
            return hashToStoreInDatabase;
        }

        /// <summary>
        /// Checks if a password matches the stored hash
        /// </summary>
        /// <param name="hashedPwdFromDatabase">Hash from database</param>
        /// <param name="userEnteredPassword">Plain text password entered by user</param>
        /// <returns>True if password matches</returns>
        public static bool CheckPasswordWithDB(string hashedPwdFromDatabase, string userEnteredPassword)
        {
            return BCrypt.CheckPassword(userEnteredPassword + AMP_SP(), hashedPwdFromDatabase);
        }

        // Additional common utility methods can be added here as needed

        /// <summary>
        /// Retrieves group queue for workflow with role-based filtering
        /// </summary>
        /// <param name="companyID">Company identifier</param>
        /// <param name="Connstring">Database connection string</param>
        /// <param name="workFlowID">Workflow identifier</param>
        /// <param name="userID">User identifier for role filtering</param>
        /// <returns>List of workflow indicators with transaction IDs and lock status</returns>
        /// <remarks>
        /// This method performs complex role-based filtering by:
        /// 1. Retrieving user's workflow roles from WF_WFRoleUser table
        /// 2. Filtering WF_WFCase_Progress based on user roles and workflow status
        /// 3. Joining with WF_WFStepLink to ensure company-specific workflow access
        /// 4. Returns indicators with TransactionID, IndicatorType, and IsLock properties
        /// </remarks>
        public static List<Indicator> GetGroupQueue(int companyID, string Connstring, int workFlowID, int userID)
        {
            string text = string.Empty;
            List<Indicator> list = new List<Indicator>();
            object obj = null;
            dynamic val = obj;

            try
            {
                // Using ADO.NET to fetch WFRole_IDs for the given userID
                List<int> list2 = new List<int>();
                using (SqlConnection connection = new SqlConnection(Connstring))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand("SELECT WFRole_ID FROM WF_WFRoleUser WHERE UserID = @userID ORDER BY WFRoleUser_ID DESC", connection))
                    {
                        cmd.Parameters.AddWithValue("@userID", userID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                list2.Add(reader.GetInt32(0)); // Add WFRole_ID to list2
                            }
                        }
                    }
                }

                // Create comma-separated string of WFRole_IDs
                for (int j = 0; j < list2.Count; j++)
                {
                    text = (j == list2.Count - 1) ? (text + list2[j]) : (text + list2[j] + ",");
                }

                // ADO.NET SQL to retrieve data from GNM_WFCase_Progress
                List<WF_WFCase_Progress> outer = new List<WF_WFCase_Progress>();
                string sql = "SELECT * FROM GNM_WFCase_Progress WHERE (Action_Chosen = 0 OR Action_Chosen IS NULL) AND WorkFlow_ID = @workFlowID AND Addresse_ID IN (" + text + ") AND Addresse_Flag = 0";
                using (SqlConnection connection = new SqlConnection(Connstring))
                {
                    connection.Open();
                    using (SqlCommand cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);
                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFCase_Progress progress = new WF_WFCase_Progress
                                {
                                    Transaction_ID = reader["Transaction_ID"] != DBNull.Value ? (int)reader["Transaction_ID"] : 0,
                                    WorkFlow_ID = reader["WorkFlow_ID"] != DBNull.Value ? (int)reader["WorkFlow_ID"] : 0,
                                    // Add other necessary properties
                                };
                                outer.Add(progress);
                            }
                        }
                    }
                }

                // ADO.NET SQL to retrieve data from WF_WFStepLink and join with the previous result
                using (SqlConnection connection = new SqlConnection(Connstring))
                {
                    connection.Open();
                    string innerSql = "SELECT WorkFlow_ID FROM GNM_WFStepLink WHERE Company_ID = @companyID AND WorkFlow_ID = @workFlowID";
                    using (SqlCommand cmd = new SqlCommand(innerSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@companyID", companyID);
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int innerWorkFlowID = reader.GetInt32(0);

                                var result = (from a in outer
                                              where a.WorkFlow_ID == innerWorkFlowID
                                              select new { a.Transaction_ID }).Distinct();

                                foreach (var item in result)
                                {
                                    Indicator indicator = new Indicator
                                    {
                                        TransactionID = item.Transaction_ID,
                                        IndicatorType = 0,
                                        IsLock = false
                                    };
                                    list.Add(indicator);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
            }

            return list;
        }


        /// <summary>
        /// Retrieves all queue items for workflow with comprehensive filtering and status checking
        /// </summary>
        /// <param name="companyID">Company identifier for filtering</param>
        /// <param name="workFlowID">Workflow identifier</param>
        /// <param name="userID">User identifier for access control</param>
        /// <param name="StatusID">Status identifier for filtering workflow steps</param>
        /// <param name="branchID">Branch identifier for branch-specific filtering</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="LogException">Flag to enable exception logging (1 = enabled, 0 = disabled)</param>
        /// <returns>List of workflow indicators representing all accessible queue items</returns>
        /// <remarks>
        /// This method performs comprehensive queue retrieval by:
        /// 1. Checking workflow branch filtering settings (AllQueue_Filter_IsBranch)
        /// 2. Retrieving workflow step status and end step type configurations
        /// 3. Filtering active workflow steps based on status and workflow ID
        /// 4. Joining workflow case progress with step links for company-specific access
        /// 5. Applying branch-based filtering when enabled
        /// 6. Returns indicators with transaction details and lock status
        /// </remarks>
        public static List<Indicator> GetAllQueue(int companyID, int workFlowID, int userID, int StatusID, int branchID, string connString, int LogException)
        {
            bool flag = false;
            List<int> CloseStepID = new List<int>();
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "SELECT TOP 1 AllQueue_Filter_IsBranch FROM GNM_WorkFlow WHERE WorkFlow_ID = @workFlowID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@workFlowID", workFlowID);




                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        var result = cmd.ExecuteScalar();
                        if (result != DBNull.Value)
                        {
                            flag = Convert.ToBoolean(result);
                        }



                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            List<Indicator> list = new List<Indicator>();
            WF_WFStepStatus StepStutus = null;
            List<WF_WFStepLink> WFStepLinksList = new List<WF_WFStepLink>();
            IEnumerable<WF_WFStepLink> inner = null;
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = @"SELECT * 
                     FROM GNM_WFStepLink 
                     WHERE Company_ID = @companyID";

                SqlCommand cmd = null;

                try
                {
                    using (cmd = new SqlCommand(query, conn))
                    {
                        cmd.CommandType = CommandType.Text;
                        cmd.Parameters.AddWithValue("@companyID", companyID);



                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                WF_WFStepLink stepLink = new WF_WFStepLink
                                {
                                    WFStepLink_ID = reader.GetInt32(reader.GetOrdinal("WFStepLink_ID")),
                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                    Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                    FrmWFSteps_ID = reader.GetInt32(reader.GetOrdinal("FrmWFSteps_ID")),
                                    WFAction_ID = reader.GetInt32(reader.GetOrdinal("WFAction_ID")),
                                    ToWFSteps_ID = reader.GetInt32(reader.GetOrdinal("ToWFSteps_ID")),
                                    Addresse_WFRole_ID = reader.GetInt32(reader.GetOrdinal("Addresse_WFRole_ID")),
                                    Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                    IsSMSSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToCustomer")),
                                    IsEmailSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToCustomer")),
                                    IsSMSSentToAddressee = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToAddressee")),
                                    IsEmailSentToAddresse = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToAddresse")),
                                    AutoAllocationAllowed = reader.GetBoolean(reader.GetOrdinal("AutoAllocationAllowed")),
                                    IsVersionEnabled = reader.GetBoolean(reader.GetOrdinal("IsVersionEnabled")),
                                    InvokeParentWF_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWF_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeParentWF_ID")),
                                    InvokeParentWFLink_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWFLink_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeParentWFLink_ID")),
                                    InvokeChildObject_ID = reader.IsDBNull(reader.GetOrdinal("InvokeChildObject_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("InvokeChildObject_ID")),
                                    InvokeChildObjectAction = reader.IsDBNull(reader.GetOrdinal("InvokeChildObjectAction")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObjectAction")),

                                    WFField_ID = reader.IsDBNull(reader.GetOrdinal("WFField_ID")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("WFField_ID")),
                                    AutoCondition = reader.IsDBNull(reader.GetOrdinal("AutoCondition")) ? null : reader.GetString(reader.GetOrdinal("AutoCondition"))
                                };

                                WFStepLinksList.Add(stepLink);
                            }
                            inner = WFStepLinksList.AsEnumerable();
                        }

                    }


                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                    }

                }
                finally
                {
                    cmd.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }

            }
            int ID = 0;
            object obj = null;
            dynamic val = obj;
            int EndStepTypeID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string endStepTypeQuery = "SELECT TOP 1 WFStepType_ID FROM GNM_WFStepType WHERE UPPER(WFStepType_Nm) = 'END'";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(endStepTypeQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;




                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            var result = cmd.ExecuteScalar();
                            if (result != DBNull.Value)
                            {
                                EndStepTypeID = int.Parse(result.ToString());
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string closeStepIDQuery = @"
                    SELECT WFSteps_ID 
                    FROM GNM_WFSteps 
                    WHERE WFStepType_ID = @EndStepTypeID AND WorkFlow_ID = @WorkFlowID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(closeStepIDQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@EndStepTypeID", EndStepTypeID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {

                                    int WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID"));
                                    CloseStepID.Add(WFSteps_ID);
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }


                List<int> CloseStepIDStore = new List<int>();
                CloseStepIDStore.AddRange(CloseStepID);
                List<WF_WFCase_Progress> list2 = new List<WF_WFCase_Progress>();
                IEnumerable<WF_WFSteps> enumerable = null;

                int roleID = 0;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string roleIDQuery = @"
                    SELECT TOP 1 a.WFRole_ID
                    FROM GNM_WFRoleUser a
                    JOIN GNM_WFRole role ON a.WFRole_ID = a.WFRole_ID
                    WHERE role.WorkFlow_ID = @WorkFlowID AND a.UserID = @UserID";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(roleIDQuery, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                            cmd.Parameters.AddWithValue("@UserID", userID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {

                                    roleID = reader.GetInt32(reader.GetOrdinal("WFRole_ID"));
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }
                if (StatusID == 0)
                {
                    list2 = new List<WF_WFCase_Progress>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"SELECT WFCaseProgress_ID, WorkFlow_ID, Transaction_ID, WFSteps_ID, Addresse_ID, Addresse_Flag, 
                       Received_Time, Actioned_By, Action_Time, Action_Chosen, Action_Remarks, Locked_Ind, WFNextStep_ID
                FROM GNM_WFCase_Progress
                WHERE (WFNextStep_ID IN (@CloseStepIDs) OR Action_Chosen IS NULL) AND WorkFlow_ID = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                string closeStepIDList = string.Join(",", CloseStepID);
                                cmd.CommandText = cmd.CommandText.Replace("@CloseStepIDs", closeStepIDList);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFCase_Progress item = new WF_WFCase_Progress
                                        {
                                            WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            Transaction_ID = reader.IsDBNull(reader.GetOrdinal("Transaction_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("Transaction_ID")),

                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                            Addresse_Flag = (byte)(reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? 0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag"))),
                                            Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                            Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                            Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                            Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                            Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                            Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                            WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                        };
                                        list2.Add(item);
                                    }
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                }
                else
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string queryStatus = @"SELECT TOP (1) [WFStepStatus_ID], [WFStepStatus_Nm], [StepStatusCode] 
                      FROM [dbo].[GNM_WFStepStatus] 
                      WHERE [WFStepStatus_ID] = @StatusID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(queryStatus, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@StatusID", StatusID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        StepStutus = new WF_WFStepStatus
                                        {
                                            WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                            WFStepStatus_Nm = reader.GetString(reader.GetOrdinal("WFStepStatus_Nm")),
                                            StepStatusCode = reader.GetString(reader.GetOrdinal("StepStatusCode"))
                                        };
                                    }
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }
                    List<WF_WFSteps> queryStepsRes = new List<WF_WFSteps>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string querySteps = @"SELECT [WFSteps_ID], [WorkFlow_ID], [WFStep_Name], [WFStepType_ID], [WFStepStatus_ID], [WFStep_IsActive], [BranchCode]
                         FROM [dbo].[GNM_WFSteps]
                         WHERE [WFStepStatus_ID] = @WFStepStatus_ID
                         AND [WorkFlow_ID] = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(querySteps, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@WFStepStatus_ID", StepStutus.WFStepStatus_ID);
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);


                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFSteps stepItem = new WF_WFSteps
                                        {
                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            WFStep_Name = reader.GetString(reader.GetOrdinal("WFStep_Name")),
                                            WFStepType_ID = reader.GetInt32(reader.GetOrdinal("WFStepType_ID")),
                                            WFStepStatus_ID = reader.GetInt32(reader.GetOrdinal("WFStepStatus_ID")),
                                            WFStep_IsActive = reader.GetBoolean(reader.GetOrdinal("WFStep_IsActive")),
                                            BranchCode = reader.IsDBNull(reader.GetOrdinal("BranchCode")) ? null : reader.GetString(reader.GetOrdinal("BranchCode"))
                                        };
                                        queryStepsRes.Add(stepItem);
                                    }
                                    enumerable = queryStepsRes.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }


                    WF_WFSteps step;
                    foreach (WF_WFSteps item in enumerable)
                    {
                        step = item;
                        int num = 0;
                        while (num < CloseStepID.Count())
                        {
                            if (step.WFSteps_ID == CloseStepID[num])
                            {
                                ID = CloseStepID[num];
                                using (SqlConnection conn = new SqlConnection(connString))
                                {
                                    string queryProgress = @"SELECT [WFCaseProgress_ID],
                                        [WorkFlow_ID],
                                        [Transaction_ID],
                                        [WFSteps_ID],
                                        [Addresse_ID],
                                        [Addresse_Flag],
                                        [Received_Time],
                                        [Actioned_By],
                                        [Action_Time],
                                        [Action_Chosen],
                                        [Action_Remarks],
                                        [Locked_Ind],
                                        [WFNextStep_ID]
                                 FROM [dbo].[GNM_WFCase_Progress]
                                 WHERE [WorkFlow_ID] = @WorkFlowID AND [WFNextStep_ID] = @WFNextStepID";

                                    SqlCommand cmd = null;

                                    try
                                    {
                                        using (cmd = new SqlCommand(queryProgress, conn))
                                        {
                                            cmd.CommandType = CommandType.Text;
                                            cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                            cmd.Parameters.AddWithValue("@WFNextStepID", ID);


                                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                            {
                                                conn.Open();
                                            }

                                            using (SqlDataReader reader = cmd.ExecuteReader())
                                            {
                                                while (reader.Read())
                                                {
                                                    WF_WFCase_Progress progressItem = new WF_WFCase_Progress
                                                    {
                                                        WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                                        Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                                        WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                                        Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                                        Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                                        Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                                        Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                                        Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                                        Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                                        Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                                        Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                                        WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                                    };
                                                    list2.AddRange(new[] { progressItem });
                                                }

                                            }



                                        }


                                    }
                                    catch (Exception ex)
                                    {
                                        if (LogException == 1)
                                        {
                                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                                        }

                                    }
                                    finally
                                    {
                                        cmd.Dispose();
                                        conn.Close();
                                        conn.Dispose();
                                        SqlConnection.ClearAllPools();
                                    }

                                }

                                CloseStepID.RemoveAt(0);
                                num = 0;
                                continue;
                            }
                            using (SqlConnection conn = new SqlConnection(connString))
                            {
                                string query = @"
                                SELECT [WFCaseProgress_ID], [WorkFlow_ID], [Transaction_ID], [WFSteps_ID],
                                       [Addresse_ID], [Addresse_Flag], [Received_Time], [Actioned_By], 
                                       [Action_Time], [Action_Chosen], [Action_Remarks], [Locked_Ind], [WFNextStep_ID]
                                FROM [GNM_WFCase_Progress]
                                WHERE [WFSteps_ID] = @WFSteps_ID
                                  AND [WorkFlow_ID] = @WorkFlow_ID
                                  AND [Action_Chosen] IS NULL;";

                                SqlCommand cmd = null;

                                try
                                {
                                    using (cmd = new SqlCommand(query, conn))
                                    {
                                        cmd.CommandType = CommandType.Text;
                                        cmd.Parameters.AddWithValue("@WFSteps_ID", step.WFSteps_ID);
                                        cmd.Parameters.AddWithValue("@WorkFlow_ID", workFlowID);


                                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                        {
                                            conn.Open();
                                        }

                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {

                                                WF_WFCase_Progress progressItem = new WF_WFCase_Progress
                                                {
                                                    WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                                    WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                                    Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                                    WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                                    Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                                    Addresse_Flag = reader.IsDBNull(reader.GetOrdinal("Addresse_Flag")) ? (byte)0 : reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                                    Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                                    Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                                    Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                                    Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                                    Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                                    Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                                    WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                                };
                                                list2.AddRange(new[] { progressItem });
                                            }
                                            enumerable = queryStepsRes.AsEnumerable();
                                        }



                                    }


                                }
                                catch (Exception ex)
                                {
                                    if (LogException == 1)
                                    {
                                        LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                                    }

                                }
                                finally
                                {
                                    cmd.Dispose();
                                    conn.Close();
                                    conn.Dispose();
                                    SqlConnection.ClearAllPools();
                                }

                            }

                            break;
                        }
                    }
                }

                val = (from a in list2
                       join b in inner on a.WorkFlow_ID equals b.WorkFlow_ID
                       where b.WorkFlow_ID == workFlowID
                       select new { a, b }).Select(joined =>
                       {
                           int transaction_ID = joined.a.Transaction_ID;
                           int? actioned_By = joined.a.Actioned_By;
                           int? addresse_ID = joined.a.Addresse_ID;
                           int indicator2;
                           if (!CloseStepIDStore.Contains(joined.a.WFNextStep_ID.HasValue ? joined.a.WFNextStep_ID.Value : 0))
                           {
                               int? addresse_ID2 = joined.a.Addresse_ID;
                               int num2 = userID;
                               if (addresse_ID2.GetValueOrDefault() != num2 || !addresse_ID2.HasValue || joined.a.Addresse_Flag != 1)
                               {
                                   addresse_ID2 = joined.a.Addresse_ID;
                                   num2 = roleID;
                                   indicator2 = ((addresse_ID2.GetValueOrDefault() == num2 && addresse_ID2.HasValue && joined.a.Addresse_Flag == 0) ? 2 : 4);
                               }
                               else
                               {
                                   indicator2 = 1;
                               }
                           }
                           else
                           {
                               indicator2 = 3;
                           }

                           return new
                           {
                               Transaction_ID = transaction_ID,
                               Actioned_By = actioned_By,
                               Addresse_ID = addresse_ID,
                               indicator = indicator2
                           };
                       }).Distinct();
                foreach (dynamic item2 in val)
                {
                    Indicator indicator = new Indicator();
                    indicator.TransactionID = item2.Transaction_ID;
                    indicator.IndicatorType = item2.indicator;
                    indicator.IsLock = false;
                    list.Add(indicator);
                }
            }
            catch (Exception)
            {
            }

            return list;
        }

        /// <summary>
        /// Checks if a user has permission to add records for a specific workflow and object
        /// </summary>
        /// <param name="ObjectID">Object identifier to check permissions for</param>
        /// <param name="WorkFlowID">Workflow identifier</param>
        /// <param name="CompanyID">Company identifier for context</param>
        /// <param name="UserID">User identifier to check permissions for</param>
        /// <param name="connString">Database connection string</param>
        /// <param name="LogException">Flag to enable exception logging (1 = enabled, 0 = disabled)</param>
        /// <returns>True if user can add records, false otherwise</returns>
        /// <remarks>
        /// This method validates user permissions by:
        /// 1. Executing a stored procedure 'SP_CheckIsAddRecords' with provided parameters
        /// 2. Checking the return value to determine if user has add permissions
        /// 3. Returns true if the stored procedure returns a positive value
        /// 4. Handles exceptions gracefully and logs them when LogException is enabled
        /// </remarks>
        public static bool chkIsAddRecords(int ObjectID, int WorkFlowID, int CompanyID, int UserID, string connString, int LogException)
        {
            bool flag = true;
            try
            {
                int num = 0;
                IEnumerable<WF_WFStepLink> outer = null;
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"
         SELECT COUNT(a.WFStepLink_ID) 
         FROM GNM_WFStepLink a
         INNER JOIN GNM_WFRoleUser b ON a.Addresse_WFRole_ID = b.WFRole_ID
         INNER JOIN GNM_WFSteps c ON a.FrmWFSteps_ID = c.WFSteps_ID
         WHERE a.WorkFlow_ID = @WorkFlowID
         AND a.Company_ID = @CompanyID
         AND b.UserID = @UserID
         AND c.WFStepType_ID = 1";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);
                            command.Parameters.AddWithValue("@CompanyID", CompanyID);
                            command.Parameters.AddWithValue("@UserID", UserID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            num = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log exception
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                        }
                    }
                    finally
                    {
                        command?.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                flag = num > 0;
            }
            catch (Exception ex)
            {
                flag = false;
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
            }

            return flag;
        }

        /// <summary>
        /// Generic method to retrieve values from database with flexible return type support
        /// </summary>
        /// <typeparam name="T">The type of data to return (supports primitives, objects, and collections)</typeparam>
        /// <param name="query">SQL query or stored procedure name to execute</param>
        /// <param name="parameters">List of SQL parameters for the query</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="LogException">Flag to enable exception logging (1 = enabled, 0 = disabled)</param>
        /// <param name="nextResult">Whether to navigate to next result set (default: false)</param>
        /// <param name="resultSetIndex">Index of the result set to use when nextResult is true (default: 0)</param>
        /// <param name="isStoredProcedure">Whether the query is a stored procedure (default: false)</param>
        /// <returns>Data of type T populated from database query results</returns>
        /// <remarks>
        /// This generic method supports multiple return types:
        /// - **Collections**: List&lt;T&gt; - Returns a list of objects mapped from result set
        /// - **Primitives**: string, bool, int - Returns single scalar values
        /// - **Objects**: Custom classes - Returns single object with properties mapped from columns
        ///
        /// **Mapping Logic**:
        /// - For collections: Maps each row to an object and adds to list
        /// - For primitives: Converts first column of first row to target type
        /// - For objects: Maps column names to property names using reflection
        /// - Handles DBNull values gracefully
        /// - Supports nullable types and type conversion
        ///
        /// **Multiple Result Sets**:
        /// - Use nextResult=true and resultSetIndex to access specific result sets
        /// - Iterates through result sets until target index is reached
        /// </remarks>
        public static T GetValueFromDB<T>(string query, List<SqlParameter> parameters, string connectionString, int LogException, bool nextResult = false, int resultSetIndex = 0, bool isStoredProcedure = false)
        {
            T result = default;

            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                conn.Open();

                using (SqlCommand cmd = new SqlCommand(query, conn))
                {
                    cmd.CommandType = isStoredProcedure ? CommandType.StoredProcedure : CommandType.Text;
                    // Add parameters to the command
                    if (parameters != null && parameters.Count > 0)
                    {
                        cmd.Parameters.AddRange(parameters.ToArray());
                    }

                    // Execute the query
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        int currentResultSetIndex = 0;

                        // Iterate through the result sets
                        do
                        {
                            // Skip result sets until the target result set is reached
                            if (nextResult && currentResultSetIndex != resultSetIndex)
                            {
                                currentResultSetIndex++;
                                continue;
                            }

                            // If the result type is a collection (e.g., List<T>)
                            if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(List<>))
                            {
                                var list = Activator.CreateInstance<T>();
                                var columnPropertyMapping = typeof(T).GetGenericArguments()[0]
                                    .GetProperties()
                                    .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                 .Select(reader.GetName)
                                                                 .Contains(property.Name))
                                    .Select(property => new
                                    {
                                        Property = property,
                                        Ordinal = reader.GetOrdinal(property.Name),
                                        TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                    })
                                    .ToList();

                                while (reader.Read())
                                {
                                    var item = Activator.CreateInstance(typeof(T).GetGenericArguments()[0]);

                                    foreach (var mapping in columnPropertyMapping)
                                    {
                                        var value = reader[mapping.Ordinal];
                                        if (value != DBNull.Value)
                                        {
                                            // Set property value directly using the precomputed type
                                            mapping.Property.SetValue(item, Convert.ChangeType(value, mapping.TargetType));
                                        }
                                    }

                                    // Add item to list
                                    ((IList)list).Add(item);
                                }

                                result = (T)list;
                            }
                            // If the result is a scalar type (like string, bool, int)
                            else if (typeof(T) == typeof(string))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0], typeof(T));
                                }
                            }
                            else if (typeof(T) == typeof(bool))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0].ToString().ToUpper() == "TRUE", typeof(T));
                                }
                            }
                            else if (typeof(T) == typeof(int))
                            {
                                if (reader.Read())
                                {
                                    result = (T)Convert.ChangeType(reader[0], typeof(T));
                                }
                            }
                            // Handle cases for custom classes/complex objects
                            else if (typeof(T).IsClass && typeof(T) != typeof(string))
                            {
                                if (reader.Read())
                                {
                                    result = Activator.CreateInstance<T>(); // Create an instance of the class
                                    var columnPropertyMapping = typeof(T)
                                        .GetProperties()
                                        .Where(property => Enumerable.Range(0, reader.FieldCount)
                                                                     .Select(reader.GetName)
                                                                     .Contains(property.Name))
                                        .Select(property => new
                                        {
                                            Property = property,
                                            Ordinal = reader.GetOrdinal(property.Name),
                                            TargetType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType
                                        })
                                        .ToList();

                                    foreach (var mapping in columnPropertyMapping)
                                    {
                                        var value = reader[mapping.Ordinal];
                                        if (value != DBNull.Value)
                                        {
                                            // Set property value directly using the precomputed type
                                            mapping.Property.SetValue(result, Convert.ChangeType(value, mapping.TargetType));
                                        }
                                    }
                                }
                            }

                            // Increment the result set index after processing
                            currentResultSetIndex++;
                        }
                        while (reader.NextResult() && currentResultSetIndex <= resultSetIndex); // Loop until desired result set is found
                    }
                }
            }

            return result;
        }

        #region:::InitialSetup:::
        public static JsonResult InitialSetup(int ObjID, int User_ID, string connString, int LogException)
        {
            string query = string.Empty;
            List<ACLProperties> ACLobjList = new List<ACLProperties>();

            int UserID = 0;


            GNM_Object gobj = null;
            try
            {
                UserID = Convert.ToInt32(User_ID);

                try
                {


                    query = "select object_id,SUM(cast(RoleObject_Create as int)) RoleObject_Create ,SUM(cast(RoleObject_Read as int)) RoleObject_Read,sum(cast(roleobject_update as int)) RoleObject_Update, SUM(cast(roleobject_delete as int)) RoleObject_Delete, SUM(cast(roleobject_print as int)) RoleObject_Print, SUM(cast(roleobject_export as int)) RoleObject_Export ,SUM(cast(roleobject_import as int)) RoleObject_Import from GNM_roleobject where Role_ID in (select role_id from gnm_userrole where USER_ID=" + UserID + ") group by OBJECT_ID";
                    using (SqlConnection conn = new SqlConnection(connString))
                    {


                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.HasRows) // Check if there are any rows in the result set
                                    {
                                        while (reader.Read())
                                        {
                                            ACLProperties aCLProperties = new ACLProperties
                                            {
                                                Object_ID = int.Parse(reader["Object_ID"].ToString()),
                                                RoleObject_Create = Convert.ToInt32(reader["RoleObject_Create"]),
                                                RoleObject_Read = Convert.ToInt32(reader["RoleObject_Read"]),
                                                RoleObject_Update = Convert.ToInt32(reader["RoleObject_Update"]),
                                                RoleObject_Delete = Convert.ToInt32(reader["RoleObject_Delete"]),
                                                RoleObject_Print = Convert.ToInt32(reader["RoleObject_Print"]),
                                                RoleObject_Export = Convert.ToInt32(reader["RoleObject_Export"]),
                                                RoleObject_Import = Convert.ToInt32(reader["RoleObject_Import"])
                                            };

                                            ACLobjList.Add(aCLProperties);
                                        }
                                    }
                                }




                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                }
                catch (Exception ex)
                {

                }



                using (SqlConnection conn = new SqlConnection(connString))
                {

                    string Query = @"SELECT 
                            [Object_ID],
                            [Object_Name],
                            [Read_Action],
                            [Create_Action],
                            [Update_Action],
                            [Delete_Action],
                            [Export_Action],
                            [Print_Action],
                            [Object_IsActive],
                            [Object_Description],
                            [Import_Action],
                            [Object_Type]
                         FROM [HCLSoftware_AMP_MicroService_GUI].[dbo].[GNM_Object]
                         WHERE [Object_ID] = @ObjID";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(Query, conn))
                        {
                            command.CommandType = CommandType.Text;
                            command.Parameters.AddWithValue("@ObjID", ObjID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    gobj = new GNM_Object
                                    {
                                        Object_ID = Convert.ToInt32(reader["Object_ID"]),
                                        Object_Name = reader["Object_Name"].ToString(),
                                        Read_Action = reader["Read_Action"].ToString(),
                                        Create_Action = reader["Create_Action"].ToString(),
                                        Update_Action = reader["Update_Action"].ToString(),
                                        Delete_Action = reader["Delete_Action"].ToString(),
                                        Export_Action = reader["Export_Action"].ToString(),
                                        Print_Action = reader["Print_Action"].ToString(),
                                        Object_IsActive = Convert.ToBoolean(reader["Object_IsActive"]),
                                        Object_Description = reader["Object_Description"].ToString(),
                                        Import_Action = reader["Import_Action"].ToString(),
                                        Object_Type = reader["Object_Type"].ToString()
                                    };
                                }

                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

                var aclData = new
                {
                    IsRead = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Read > 0) ? true : false,
                    IsAdd = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Create > 0) ? true : false,
                    IsEdit = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Update > 0) ? true : false,
                    IsDelete = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Delete > 0) ? true : false,
                    IsPrint = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Print > 0) ? true : false,
                    IsExport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Export > 0) ? true : false,
                    IsImport = (ACLobjList.Where(obj => obj.Object_ID == ObjID).First().RoleObject_Import > 0) ? true : false,


                    SelectAction = "/" + gobj.Object_Name + "/" + gobj.Read_Action,
                    AddAction = "/" + gobj.Object_Name + "/" + gobj.Create_Action,
                    EditAction = "/" + gobj.Object_Name + "/" + gobj.Update_Action,
                    DeleteAction = "/" + gobj.Object_Name + "/" + gobj.Delete_Action,
                    PrintAction = "/" + gobj.Object_Name + "/" + gobj.Print_Action,
                    ExportAction = "/" + gobj.Object_Name + "/" + gobj.Export_Action,
                    ImportAction = "/" + gobj.Object_Name + "/" + gobj.Import_Action
                };
                return new JsonResult(aclData);
            }
            catch (Exception ex)
            {
                return null;
            }


        }
        #endregion

        static List<ACLProperties> ACLobjList = new List<ACLProperties>();
        #region  GetMyQueue
        public static bool AddAction(int ObjID)
        {
            bool IsAdd = false;
            try
            {

                if (ACLobjList != null)
                {
                    // Check if the list contains the specific object and validate the role
                    var aclObj = ACLobjList.FirstOrDefault(obj => obj.Object_ID == ObjID);
                    if (aclObj != null && aclObj.RoleObject_Create > 0)
                    {
                        IsAdd = true;
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle any exceptions here, e.g., log or rethrow
                Console.WriteLine($"Error: {ex.Message}");
            }

            return IsAdd;
        }
        #endregion

        public static void UpdateAPIDashboard(string apiCategory, string status, string connString)
        {
            string query = "EXEC Update_API_Dashboard @API_Category, @Status";

            using (SqlConnection conn = new SqlConnection(connString))
            using (SqlCommand cmd = new SqlCommand(query, conn))
            {
                cmd.Parameters.AddWithValue("@API_Category", apiCategory);
                cmd.Parameters.AddWithValue("@Status", status);

                conn.Open();
                cmd.ExecuteNonQuery();
            }
        }
        public static bool ValidateNegativeValueGreaterThanZero(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();

            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool IsNeedCheckNegativeValue = column.Value;

                var columnValue = row[columnName]?.ToString();

                if (IsNeedCheckNegativeValue)
                {
                    // If not mandatory, only validate if present and not empty
                    if (!string.IsNullOrWhiteSpace(columnValue))
                    {
                        if (!decimal.TryParse(columnValue, out decimal numericValue))
                        {
                            invalidColumns.Add($"{columnName} (invalid number)");
                            isValid = false;
                        }
                        else if (numericValue <= 0)
                        {
                            invalidColumns.Add($"{columnName} (must be greater than 0)");
                            isValid = false;
                        }
                    }
                }
            }

            return isValid;
        }
        public static bool ValidateNegativeValueGreaterThanorEqualZero(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();

            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool IsNeedCheckNegativeValue = column.Value;

                var columnValue = row[columnName]?.ToString();

                if (IsNeedCheckNegativeValue)
                {
                    // If not mandatory, only validate if present and not empty
                    if (!string.IsNullOrWhiteSpace(columnValue))
                    {
                        if (!decimal.TryParse(columnValue, out decimal numericValue))
                        {
                            invalidColumns.Add($"{columnName} (invalid number)");
                            isValid = false;
                        }
                        else if (numericValue < 0)
                        {
                            invalidColumns.Add($"{columnName} (must be zero or a positive number)");
                            isValid = false;
                        }
                    }
                }
            }

            return isValid;
        }
        public static ErrorMessage ValidatePriceData(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            StringBuilder ErrorMessage1 = new StringBuilder();
            ErrorMessage errodata = new ErrorMessage();
            errodata.IsValid = true;
            string Message = string.Empty; // Assume all are valid initially
            invalidColumns = new List<string>();
            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool isMandatory = column.Value;

                // Validate mandatory columns

                var columnValue = row[columnName]?.ToString();

                if (string.IsNullOrWhiteSpace(columnValue) || Convert.ToDecimal(row[columnName]?.ToString()) <= 0)
                {
                    ErrorMessage1.Append(" " + columnName + " cannot be empty or zero ");
                    invalidColumns.Add(columnName);
                    //LS.LogSheetExporter.LogToTextFile(0,
                    //    $"Validation Error: {columnName} is mandatory but missing or invalid.",
                    //    nameof(ValidateAndLog),
                    //    $"Row data: {row.ToString()}");

                    // Mark as invalid
                    errodata.IsValid = false;
                }



            }
            if (!errodata.IsValid)
            {
                errodata.Message = ErrorMessage1.ToString();

            }
            return errodata;
        }
        public static bool ValidateAndLog(JObject row, Dictionary<string, bool> columns, out List<string> invalidColumns)
        {
            bool isValid = true; // Assume all are valid initially
            invalidColumns = new List<string>();
            foreach (var column in columns)
            {
                string columnName = column.Key;
                bool isMandatory = column.Value;

                // Validate mandatory columns
                if (isMandatory)
                {
                    var columnValue = row[columnName]?.ToString();

                    if (string.IsNullOrWhiteSpace(columnValue))
                    {
                        invalidColumns.Add(columnName);
                        //LS.LogSheetExporter.LogToTextFile(0,
                        //    $"Validation Error: {columnName} is mandatory but missing or invalid.",
                        //    nameof(ValidateAndLog),
                        //    $"Row data: {row.ToString()}");

                        // Mark as invalid
                        isValid = false;
                    }

                }

            }

            return isValid;
        }

        public static bool CheckPartySpecific(int Party_ID, int CallComplexity_ID, int? CallPriority_ID, int CompanyID, string connString)
        {
            List<HD_ServiceLevelAgreement> ServiceLevelAgreementList = new List<HD_ServiceLevelAgreement>();

            using (var conn = new SqlConnection(connString))
            {
                conn.Open();

                string StartTimequery = "SELECT * FROM HD_ServiceLevelAgreement";

                using (var cmd = new SqlCommand(StartTimequery, conn))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var refMasterDetailObj = new HD_ServiceLevelAgreement
                            {
                                Company_ID = reader["Company_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Company_ID"]),
                                Party_ID = reader["Party_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Party_ID"]),
                                CallComplexity_ID = reader["CallComplexity_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallComplexity_ID"]),
                                CallPriority_ID = reader["CallPriority_ID"] == DBNull.Value ? 0 : Convert.ToInt32(reader["CallPriority_ID"])
                            };

                            ServiceLevelAgreementList.Add(refMasterDetailObj);
                        }
                    }
                }
            }
            List<HD_ServiceLevelAgreement> Agreement = ServiceLevelAgreementList.Where(sr => sr.Company_ID == CompanyID && sr.Party_ID == Party_ID && sr.CallComplexity_ID == CallComplexity_ID && sr.CallPriority_ID == CallPriority_ID).ToList();
            if (Agreement.Count() != 0) return true; else return false;
        }

        #region ::: CheckAutoAllocation Vinay  14-11-2024:::
        /// <summary>
        /// CheckAutoAllocation
        /// </summary>
        /// <returns>...</returns>
        public static bool CheckAutoAllocation(int companyID, int workFlowID, int UserID, string connString, int LogException)
        {
            bool result = true;
            WF_WFStepLink stepLink = null;
            IEnumerable<WF_WFCase_Progress> enumerable = null;
            List<WF_WFCase_Progress> progressList = new List<WF_WFCase_Progress>();
            try
            {
                stepLink = GetAutoAllocationStepDetails(workFlowID, companyID, connString, LogException);
                if (stepLink != null)
                {
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = @"SELECT *
                     FROM GNM_WFCase_Progress
                    WHERE WFSteps_ID = @FrmWFStepsID 
                    AND Action_Chosen IS NULL 
                    AND Addresse_Flag = 1 
                    AND Addresse_ID = @UserID 
                    AND WorkFlow_ID = @WorkFlowID";

                        SqlCommand cmd = null;

                        try
                        {
                            using (cmd = new SqlCommand(query, conn))
                            {
                                cmd.CommandType = CommandType.Text;
                                cmd.Parameters.AddWithValue("@FrmWFStepsID", stepLink.FrmWFSteps_ID);
                                cmd.Parameters.AddWithValue("@UserID", (int?)UserID);
                                cmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);



                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }

                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        WF_WFCase_Progress progress = new WF_WFCase_Progress()
                                        {
                                            WFCaseProgress_ID = reader.GetInt32(reader.GetOrdinal("WFCaseProgress_ID")),
                                            WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                            Transaction_ID = reader.GetInt32(reader.GetOrdinal("Transaction_ID")),
                                            WFSteps_ID = reader.GetInt32(reader.GetOrdinal("WFSteps_ID")),
                                            Addresse_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_ID")),
                                            Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                            Received_Time = reader.GetDateTime(reader.GetOrdinal("Received_Time")),
                                            Actioned_By = reader.IsDBNull(reader.GetOrdinal("Actioned_By")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Actioned_By")),
                                            Action_Time = reader.IsDBNull(reader.GetOrdinal("Action_Time")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("Action_Time")),
                                            Action_Chosen = reader.IsDBNull(reader.GetOrdinal("Action_Chosen")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Action_Chosen")),
                                            Action_Remarks = reader.IsDBNull(reader.GetOrdinal("Action_Remarks")) ? null : reader.GetString(reader.GetOrdinal("Action_Remarks")),
                                            Locked_Ind = reader.IsDBNull(reader.GetOrdinal("Locked_Ind")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("Locked_Ind")),
                                            WFNextStep_ID = reader.IsDBNull(reader.GetOrdinal("WFNextStep_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFNextStep_ID"))
                                        };
                                        progressList.Add(progress);
                                    }
                                    enumerable = progressList.AsEnumerable();
                                }



                            }


                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                            }

                        }
                        finally
                        {
                            cmd.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }

                    }

                    if (enumerable.Count() == 0)
                    {
                        result = false;
                    }
                }
                else
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        public static WF_WFStepLink GetAutoAllocationStepDetails(int WorkFlowID, int CompanyID, string connString, int LogException)
        {
            WF_WFStepLink result = null;
            try
            {
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = @"SELECT *
                        
                    FROM GNM_WFStepLink
                    WHERE Company_ID = @CompanyID 
                    AND WorkFlow_ID = @WorkFlowID 
                    AND AutoAllocationAllowed = 1";

                    SqlCommand cmd = null;

                    try
                    {
                        using (cmd = new SqlCommand(query, conn))
                        {
                            cmd.CommandType = CommandType.Text;
                            cmd.Parameters.AddWithValue("@CompanyID", CompanyID);
                            cmd.Parameters.AddWithValue("@WorkFlowID", WorkFlowID);



                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                if (reader.Read())
                                {
                                    result = new WF_WFStepLink()
                                    {
                                        WFStepLink_ID = reader.GetInt32(reader.GetOrdinal("WFStepLink_ID")),
                                        WorkFlow_ID = reader.GetInt32(reader.GetOrdinal("WorkFlow_ID")),
                                        Company_ID = reader.GetInt32(reader.GetOrdinal("Company_ID")),
                                        FrmWFSteps_ID = reader.GetInt32(reader.GetOrdinal("FrmWFSteps_ID")),
                                        WFAction_ID = reader.GetInt32(reader.GetOrdinal("WFAction_ID")),
                                        ToWFSteps_ID = reader.GetInt32(reader.GetOrdinal("ToWFSteps_ID")),
                                        Addresse_WFRole_ID = reader.IsDBNull(reader.GetOrdinal("Addresse_WFRole_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("Addresse_WFRole_ID")),
                                        Addresse_Flag = reader.GetByte(reader.GetOrdinal("Addresse_Flag")),
                                        IsSMSSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToCustomer")),
                                        IsEmailSentToCustomer = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToCustomer")),
                                        IsSMSSentToAddressee = reader.GetBoolean(reader.GetOrdinal("IsSMSSentToAddressee")),
                                        IsEmailSentToAddresse = reader.GetBoolean(reader.GetOrdinal("IsEmailSentToAddresse")),
                                        AutoAllocationAllowed = reader.GetBoolean(reader.GetOrdinal("AutoAllocationAllowed")),
                                        IsVersionEnabled = reader.GetBoolean(reader.GetOrdinal("IsVersionEnabled")),
                                        InvokeParentWF_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWF_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeParentWF_ID")),
                                        InvokeParentWFLink_ID = reader.IsDBNull(reader.GetOrdinal("InvokeParentWFLink_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeParentWFLink_ID")),
                                        InvokeChildObject_ID = reader.IsDBNull(reader.GetOrdinal("InvokeChildObject_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObject_ID")),
                                        InvokeChildObjectAction = reader.IsDBNull(reader.GetOrdinal("InvokeChildObjectAction")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("InvokeChildObjectAction")),
                                        WFField_ID = reader.IsDBNull(reader.GetOrdinal("WFField_ID")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("WFField_ID")),
                                        AutoCondition = reader.IsDBNull(reader.GetOrdinal("AutoCondition")) ? null : reader.GetString(reader.GetOrdinal("AutoCondition"))
                                    };
                                }
                            }



                        }


                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                        }

                    }
                    finally
                    {
                        cmd.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }

                }

            }
            catch (Exception ex)
            {
                LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
            }

            return result;
        }
        #endregion

        #region:: LocalTime Vinay :::
        /// <summary>
        /// Converts server time to local time based on branch timezone
        /// </summary>
        public static DateTime LocalTimeBasedOnBranch(int BranchID, DateTime servertime, string connstring)
        {
            DateTime localtime = DateTime.Now;
            string StandardTimeZone = "India Standard Time";
            try
            {

                // using (conn = new SqlConnection(ConfigurationManager.ConnectionStrings["EPC"].ConnectionString))
                using (var conn = new SqlConnection(connstring))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken) { conn.Open(); }
                    DataTable dt = new DataTable();
                    var sqlComm = new SqlCommand("SELECT TimeZoneID FROM GNM_Branch WHERE Branch_ID = @BranchID", conn);
                    sqlComm.Parameters.AddWithValue("@BranchID", BranchID);
                    int? Timezoneid = sqlComm.ExecuteScalar() as int?;
                    if (Timezoneid.HasValue)
                    {
                        SqlCommand cmdTimeZone = new SqlCommand("SELECT RefMasterDetail_Name FROM GNM_RefMasterDetail WHERE RefMasterDetail_ID = @TimezoneID", conn);
                        cmdTimeZone.Parameters.AddWithValue("@TimezoneID", Timezoneid.Value);

                        object result = cmdTimeZone.ExecuteScalar();
                        if (result != DBNull.Value && result != null)
                        {
                            StandardTimeZone = result.ToString();
                        }
                    }
                    StandardTimeZone = string.IsNullOrEmpty(StandardTimeZone) ? "India Standard Time" : StandardTimeZone;
                    localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                }
            }

            catch (Exception ex)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), connstring);
            }
            return localtime;
        }

        /// <summary>
        /// Validates call date and PCD (Planned Completion Date) for business logic compliance
        /// </summary>
        /// <param name="pcd">Planned Completion Date to validate</param>
        /// <param name="calldate">Call date to validate against</param>
        /// <returns>JsonResult containing validation result</returns>
        /// <remarks>
        /// This is a placeholder implementation. The actual validation logic should be implemented
        /// based on business requirements.
        /// </remarks>
        public static JsonResult ValidateCalldateandPCD(DateTime pcd, DateTime calldate)
        {
            try
            {
                // Placeholder implementation - replace with actual business logic
                bool isValid = pcd >= calldate;

                return new JsonResult(new
                {
                    Success = isValid,
                    Message = isValid ? "Validation successful" : "PCD must be greater than or equal to call date",
                    Data = new { PCD = pcd, CallDate = calldate }
                });
            }
            catch (Exception ex)
            {
                return new JsonResult(new
                {
                    Success = false,
                    Message = "Validation failed: " + ex.Message,
                    Data = (object)null
                });
            }
        }

        /// <summary>
        /// Checks bay workshop availability for scheduling purposes
        /// </summary>
        /// <param name="expectedArrivalDate">Expected arrival date</param>
        /// <param name="expectedDepartureDate">Expected departure date</param>
        /// <param name="isWIPBay">Whether this is a WIP (Work In Progress) bay</param>
        /// <param name="bookingMinutes">Duration of booking in minutes</param>
        /// <param name="serviceRequestId">Service request identifier</param>
        /// <param name="quotationId">Quotation identifier</param>
        /// <param name="branch">Branch identifier</param>
        /// <param name="connectionString">Database connection string</param>
        /// <param name="logException">Flag to enable exception logging</param>
        /// <returns>JsonResult containing availability information</returns>
        /// <remarks>
        /// This is a placeholder implementation. The actual availability checking logic should be implemented
        /// based on business requirements and database schema.
        /// </remarks>
        public static JsonResult CheckBayWorkshopAvailability(DateTime expectedArrivalDate, DateTime expectedDepartureDate, bool isWIPBay, int bookingMinutes, int serviceRequestId, int quotationId, int branch, string connectionString, int logException)
        {
            try
            {
                // Placeholder implementation - replace with actual business logic
                // This would typically check database for bay availability, conflicts, etc.

                bool isAvailable = true; // Placeholder logic

                return new JsonResult(new
                {
                    Success = true,
                    IsAvailable = isAvailable,
                    Message = isAvailable ? "Bay is available" : "Bay is not available for the requested time",
                    Data = new
                    {
                        ExpectedArrivalDate = expectedArrivalDate,
                        ExpectedDepartureDate = expectedDepartureDate,
                        IsWIPBay = isWIPBay,
                        BookingMinutes = bookingMinutes,
                        ServiceRequestId = serviceRequestId,
                        QuotationId = quotationId,
                        Branch = branch
                    }
                });
            }
            catch (Exception ex)
            {
                if (logException == 1)
                {
                    LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace);
                }

                return new JsonResult(new
                {
                    Success = false,
                    IsAvailable = false,
                    Message = "Error checking bay availability: " + ex.Message,
                    Data = (object)null
                });
            }
        }
        #endregion

        #region:: LocalTime:::
        /// <summary>
        // Added by DK  08-NOV-2023 to get local time zone info
        /// </summary> 
        public static DateTime LocalTime(int UserID, DateTime servertime, string connstring)
        {
            DateTime localtime = DateTime.Now;
            string StandardTimeZone = "";
            try
            {

                //using (conn = new SqlConnection(ConfigurationManager.ConnectionStrings["EPC"].ConnectionString))
                using (var conn = new SqlConnection(connstring))
                {
                    if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken) { conn.Open(); }
                    DataTable dt = new DataTable();
                    var sqlComm = new SqlCommand("SELECT TimeZone FROM MA_User WHERE User_ID='" + UserID + "'", conn);
                    dt.Load(sqlComm.ExecuteReader());
                    if (dt.Rows.Count > 0)
                    {
                        StandardTimeZone = (dt.Rows[0].ItemArray[0].ToString());
                        StandardTimeZone = StandardTimeZone == null ? "India Standard Time" : StandardTimeZone;
                        localtime = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(servertime, TimeZoneInfo.Local.Id, StandardTimeZone);
                    }
                }
            }

            catch (Exception ex)
            {
                var Request = new System.Net.Http.HttpRequestMessage();
                //ExceptionLogger.ErrorLog(ex, Request, Convert.ToInt32(UserID), connstring);
            }
            return localtime;
        }
        #endregion

    }
}
