# PowerShell script to build Docker images for all PBC Microservices

Write-Host "Building Docker images for PBC Microservices..." -ForegroundColor Green
Write-Host ""

# Define all PBC services
$services = @("PBC.UtilityService", "PBC.CoreService", "PBC.HelpdeskService", "PBC.AggregatorService", "PBC.WorkflowService")
$tags = @("pbc.utilityservice", "pbc.coreservice", "pbc.helpdeskservice", "pbc.aggregatorservice", "pbc.workflowservice")
$ports = @("5003", "5001", "5002", "5004", "5005")

$totalServices = $services.Count

for ($i = 0; $i -lt $totalServices; $i++) {
    $servicePath = $services[$i]
    $imageTag = $tags[$i]
    $servicePort = $ports[$i]
    $currentService = $i + 1

    Write-Host "[$currentService/$totalServices] Building $servicePath (Port: $servicePort)..." -ForegroundColor Yellow

    # Check if service folder exists
    if (-Not (Test-Path $servicePath)) {
        Write-Host "⚠️  Service folder not found: $servicePath" -ForegroundColor DarkYellow
        continue
    }

    $dockerfilePath = Join-Path $servicePath "Dockerfile"
    if (-Not (Test-Path $dockerfilePath)) {
        Write-Host "⚠️  No Dockerfile found in $servicePath" -ForegroundColor DarkYellow
        continue
    }

    Write-Host "   📁 Service path: $servicePath" -ForegroundColor DarkGray
    Write-Host "   🐳 Dockerfile: $dockerfilePath" -ForegroundColor DarkGray
    Write-Host "   🏷️  Image tag: $imageTag`:latest" -ForegroundColor DarkGray

    # Build Docker image
    $buildStartTime = Get-Date

    Write-Host "   🔨 Running: docker build --file $dockerfilePath --tag $imageTag`:latest --build-arg SERVICE_PORT=$servicePort $servicePath" -ForegroundColor DarkGray
    Write-Host ""

    # Execute Docker build command directly
    docker build --file $dockerfilePath --tag "$imageTag`:latest" --build-arg "SERVICE_PORT=$servicePort" $servicePath
    $buildExitCode = $LASTEXITCODE

    if ($buildExitCode -ne 0) {
        Write-Host "❌  Failed to build $imageTag" -ForegroundColor Red
        Write-Host "   Exit code: $buildExitCode" -ForegroundColor Red
        exit $buildExitCode
    } else {
        $buildEndTime = Get-Date
        $buildDuration = ($buildEndTime - $buildStartTime).TotalSeconds
        Write-Host "✅  $imageTag built successfully in $([math]::Round($buildDuration, 1)) seconds" -ForegroundColor Green
    }

    Write-Host ""
}

Write-Host "🎉 All Docker build tasks completed successfully!" -ForegroundColor Cyan
Write-Host ""
Write-Host "Built images:" -ForegroundColor Green
for ($i = 0; $i -lt $totalServices; $i++) {
    Write-Host "  • $($tags[$i]):latest (Port: $($ports[$i]))" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "To run the services with Docker Compose, use:" -ForegroundColor Yellow
Write-Host "  docker-compose up -d" -ForegroundColor White
Write-Host ""
Write-Host "To view running containers:" -ForegroundColor Yellow
Write-Host "  docker ps" -ForegroundColor White
Write-Host ""

exit 0
