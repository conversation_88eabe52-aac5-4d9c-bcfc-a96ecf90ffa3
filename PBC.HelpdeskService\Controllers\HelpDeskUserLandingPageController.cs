using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PBC.HelpdeskService.Models;
using PBC.HelpdeskService.Services;
using System;
using System.Threading.Tasks;

namespace PBC.HelpdeskService.Controllers
{
    /// <summary>
    /// Controller for Help Desk User Landing Page operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HelpDeskUserLandingPageController : ControllerBase
    {
        private readonly IHelpDeskUserLandingPageServices _helpDeskUserLandingPageServices;
        private readonly IConfiguration _configuration;
        private readonly ILogger<HelpDeskUserLandingPageController> _logger;

        public HelpDeskUserLandingPageController(
            IHelpDeskUserLandingPageServices helpDeskUserLandingPageServices,
            IConfiguration configuration,
            ILogger<HelpDeskUserLandingPageController> logger)
        {
            _helpDeskUserLandingPageServices = helpDeskUserLandingPageServices;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Select dealer name based on search criteria
        /// </summary>
        /// <param name="request">Dealer name selection request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Dealer information</returns>
        /// <response code="200">Returns dealer information</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("select-dealer-name")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SelectDealerName([FromBody] SelectDealerNameList request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/select-dealer-name - Processing dealer name selection");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.SelectDealerNameAsync(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing dealer name selection");
                return StatusCode(500, "An error occurred while processing dealer name selection");
            }
        }

        /// <summary>
        /// Get initial setup data for help desk user landing page
        /// </summary>
        /// <param name="request">Initial setup request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Initial setup data</returns>
        /// <response code="200">Returns initial setup data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("initial-setup")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> InitialSetup([FromBody] InitialSetupList request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/initial-setup - Processing initial setup");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.InitialSetup(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing initial setup");
                return StatusCode(500, "An error occurred while processing initial setup");
            }
        }

        /// <summary>
        /// Get object ID by namenote
        /// </summary>
        /// <param name="name">Object name</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Object ID</returns>
        /// <response code="200">Returns object ID</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("object-id/{name}")]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<int>> GetObjectId(string name, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("GET /api/helpdeskuserlandingpage/object-id/{Name} - Getting object ID", name);

                if (string.IsNullOrWhiteSpace(name))
                {
                    return BadRequest("Name parameter is required");
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                // Use a default query for object ID lookup
                var query = @"SELECT TOP 1 Object_ID FROM GNM_Object WHERE UPPER(Object_Name) = @Description";
                var result = await _helpDeskUserLandingPageServices.GetObjectIDAsync(name.ToUpper(), connString, query, logException);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting object ID for name: {Name}", name);
                return StatusCode(500, "An error occurred while getting object ID");
            }
        }

        /// <summary>
        /// Get branch name by ID
        /// </summary>
        /// <param name="branchId">Branch ID</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Branch name</returns>
        /// <response code="200">Returns branch name</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("branch-name/{branchId}")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<string>> GetBranchName(int branchId, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("GET /api/helpdeskuserlandingpage/branch-name/{BranchId} - Getting branch name", branchId);

                if (branchId <= 0)
                {
                    return BadRequest("Branch ID must be greater than 0");
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetBranchNameAsync(connString, logException, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting branch name for ID: {BranchId}", branchId);
                return StatusCode(500, "An error occurred while getting branch name");
            }
        }

        /// <summary>
        /// Get tabs data for company
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>List of tab positions</returns>
        /// <response code="200">Returns tabs data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("tabs-data/{companyId}")]
        [ProducesResponseType(typeof(System.Collections.Generic.List<TabPosition>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<System.Collections.Generic.List<TabPosition>>> GetTabsData(int companyId, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("GET /api/helpdeskuserlandingpage/tabs-data/{CompanyId} - Getting tabs data", companyId);

                if (companyId <= 0)
                {
                    return BadRequest("Company ID must be greater than 0");
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetTabsDataAsync(companyId, connString, logException);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tabs data for company ID: {CompanyId}", companyId);
                return StatusCode(500, "An error occurred while getting tabs data");
            }
        }

        // Batch 2 Endpoints - Permission and Validation Methods

        /// <summary>
        /// Check add permissions for a user
        /// </summary>
        /// <param name="request">Permission check request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>True if user has add permissions</returns>
        /// <response code="200">Returns permission check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-add-permissions")]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<bool>> CheckAddPermissions([FromBody] CheckPermissionsRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/check-add-permissions - Checking permissions for: {Name}", request.Name);

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = request.ConnectionString ?? _configuration.GetConnectionString("DefaultConnection");

                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.CheckAddPermissionsAsync(
                    request.Name, request.WFName, request.HelpDesk,
                    request.CompanyId, logException, 742, connString);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking add permissions for: {Name}", request.Name);
                return StatusCode(500, "An error occurred while checking add permissions");
            }
        }

        /// <summary>
        /// Validate call date and PCD
        /// </summary>
        /// <param name="request">Date validation request</param>
        /// <returns>Validation result (1 if PCD is before call date, 0 otherwise)</returns>
        /// <response code="200">Returns validation result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("validate-calldate-pcd")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> ValidateCalldateAndPCD([FromBody] validateCalldateandPCDList request)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/validate-calldate-pcd - Validating dates");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _helpDeskUserLandingPageServices.ValidateCalldateandPCDAsync(request);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating call date and PCD");
                return StatusCode(500, "An error occurred while validating dates");
            }
        }

        /// <summary>
        /// Check bay and workshop availability
        /// </summary>
        /// <param name="request">Workshop availability request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Availability status</returns>
        /// <response code="200">Returns availability status</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-bay-workshop-availability")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CheckBayWorkshopAvailability([FromBody] CheckBayWorkshopAvailabilityList request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/check-bay-workshop-availability - Checking availability");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.CheckBayWorkshopAvailabilityAsync(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking bay workshop availability");
                return StatusCode(500, "An error occurred while checking bay workshop availability");
            }
        }

        /// <summary>
        /// Check for workshop block overlap
        /// </summary>
        /// <param name="request">Workshop overlap request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Overlap status</returns>
        /// <response code="200">Returns overlap status</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-workshop-block-overlap")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CheckForWorkshopBlockOverlap([FromBody] CheckForWorkshopBlockOverlapList request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/check-workshop-block-overlap - Checking overlap");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.CheckForWorkshopBlockOverlapAsync(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking workshop block overlap");
                return StatusCode(500, "An error occurred while checking workshop block overlap");
            }
        }


        /// <summary>
        /// Select field search party with pagination and filtering
        /// </summary>
        /// <param name="request">Field search request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Paginated search results</returns>
        /// <response code="200">Returns search results</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("select-field-search-party")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SelectFieldSearchParty([FromBody] SelectFieldSearchPartyRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/select-field-search-party - Searching parties");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.SelectFieldSearchPartyAsync(
                    request.Obj,
                    connString,
                    logException,
                    request.Sidx,
                    request.Sord,
                    request.Page,
                    request.Rows,
                    request.Search,
                    request.Filters
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchParty");
                return StatusCode(500, "An error occurred while searching parties");
            }
        }

        /// <summary>
        /// Get customer data with autocomplete functionality
        /// </summary>
        /// <param name="request">Customer data request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Customer data</returns>
        /// <response code="200">Returns customer data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-customer-data")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetCustomerData([FromBody] GetCustomerDataRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/get-customer-data - Getting customer data");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetDataAsync(
                    request.Obj,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerData");
                return StatusCode(500, "An error occurred while getting customer data");
            }
        }

        /// <summary>
        /// Get dealer data with autocomplete functionality
        /// </summary>
        /// <param name="request">Dealer data request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Dealer data</returns>
        /// <response code="200">Returns dealer data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-dealer-data")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetDealerData([FromBody] GetDealerDataRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/get-dealer-data - Getting dealer data");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetDealerData(
                    request.Obj,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDealerData");
                return StatusCode(500, "An error occurred while getting dealer data");
            }
        }

        /// <summary>
        /// Get product details by product ID
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Product details</returns>
        /// <response code="200">Returns product details</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-product-details")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetProductDetails([FromBody] GetProductDetailsRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/get-product-details - Getting product details");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetProductDetails(
                    request.Obj,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetails");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }

        /// <summary>
        /// Check for duplicate contact person
        /// </summary>
        /// <param name="request">Duplicate contact person check request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Duplicate check result (0 = no duplicate, 1 = duplicate found)</returns>
        /// <response code="200">Returns duplicate check result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("check-duplicate-contact-person")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> CheckDuplicateContactPerson([FromBody] CheckDuplicateContactPersonRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/check-duplicate-contact-person - Checking duplicate contact person");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.checkDuplicateContactPersonAsync(
                    request.Obj,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckDuplicateContactPerson");
                return StatusCode(500, "An error occurred while checking duplicate contact person");
            }
        }

        /// <summary>
        /// Get open campaign details for a product
        /// </summary>
        /// <param name="request">Campaign details request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Campaign details</returns>
        /// <response code="200">Returns campaign details</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-open-campaign-details")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetOpenCampaignDetails([FromBody] GetOpenCampaignDetailsRequest request, [FromQuery] string connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/get-open-campaign-details - Getting campaign details");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetOpenCampaignDetailsAsync(
                    request.Obj,
                    request.Sidx,
                    request.Rows,
                    request.Page,
                    request.Sord,
                    request.Search,
                    request.Nd,
                    request.Filters,
                    request.Advnce,
                    request.Query,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOpenCampaignDetails");
                return StatusCode(500, "An error occurred while getting campaign details");
            }
        }


        /// <summary>
        /// Get initial data for help desk user landing page
        /// </summary>
        /// <param name="request">Initial data request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Initial data</returns>
        /// <response code="200">Returns initial data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("get-initial-data")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetInitialData([FromBody] GetInitialDataList request, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/get-initial-data - Getting initial data");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.GetInitialData(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting initial data");
                return StatusCode(500, "An error occurred while getting initial data");
            }
        }

        /// <summary>
        /// Select service request records with pagination and filtering
        /// </summary>
        /// <param name="request">Service request selection request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Service request records</returns>
        /// <response code="200">Returns service request records</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("select-service-request")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SelectServiceRequest([FromBody] SelectServiceRequestRequest request, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/select-service-request - Selecting service requests");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.SelectServiceRequest(
                    request.Obj,
                    request.Sidx,
                    request.Rows,
                    request.Page,
                    request.Sord,
                    request.Search,
                    request.Nd,
                    request.Filters,
                    request.Advnce,
                    request.Query,
                    connString,
                    logException
                );
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error selecting service requests");
                return StatusCode(500, "An error occurred while selecting service requests");
            }
        }

        /// <summary>
        /// Get tab details for a specific tab
        /// </summary>
        /// <param name="id">Tab ID</param>
        /// <param name="param">Parameter name</param>
        /// <param name="companyId">Company ID</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Tab details</returns>
        /// <response code="200">Returns tab details</response>
        /// <response code="400">Invalid request parameters</response>
        /// <response code="500">Internal server error</response>
        [HttpGet("get-tab-details")]
        [ProducesResponseType(typeof(TabPosition), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> GetTabDetails([FromQuery] int id, [FromQuery] string param, [FromQuery] int companyId, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("GET /api/helpdeskuserlandingpage/get-tab-details - Getting tab details for ID: {Id}", id);

                if (id <= 0 || string.IsNullOrWhiteSpace(param) || companyId <= 0)
                {
                    return BadRequest("ID, param, and companyId are required and must be valid");
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");

                var result = await _helpDeskUserLandingPageServices.GetTabDetailsAsync(id, param, companyId, connString);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tab details for ID: {Id}", id);
                return StatusCode(500, "An error occurred while getting tab details");
            }
        }

        /// <summary>
        /// Select workflow summary data
        /// </summary>
        /// <param name="request">Workflow summary request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Workflow summary data</returns>
        /// <response code="200">Returns workflow summary data</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("select-workflow-summary")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SelWorkFlowSummary([FromBody] GetWorkFlowSummaryList request, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/select-workflow-summary - Getting workflow summary");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.SelWorkFlowSummary(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting workflow summary");
                return StatusCode(500, "An error occurred while getting workflow summary");
            }
        }

        /// <summary>
        /// Save customer details
        /// </summary>
        /// <param name="request">Customer save request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Save result</returns>
        /// <response code="200">Returns save result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("save-customer")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> SaveCustomer([FromBody] SaveCustomerList request, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/save-customer - Saving customer");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.SaveCustomer(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving customer");
                return StatusCode(500, "An error occurred while saving customer");
            }
        }


        #region ::: UpdateIsEditTicket :::
        /// <summary>
        /// Update edit ticket status
        /// </summary>
        /// <param name="request">Update edit ticket request</param>
        /// <param name="connectionString">Database connection string (optional, will use default if not provided)</param>
        /// <returns>Update result</returns>
        /// <response code="200">Returns update result</response>
        /// <response code="400">Invalid request data</response>
        /// <response code="500">Internal server error</response>
        [HttpPost("update-is-edit-ticket")]
        [ProducesResponseType(typeof(object), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(500)]
        public async Task<IActionResult> UpdateIsEditTicket([FromBody] UpdateIsEditTicketList request, [FromQuery] string? connectionString = null)
        {
            try
            {
                _logger.LogInformation("POST /api/helpdeskuserlandingpage/update-is-edit-ticket - Updating edit ticket status");

                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Use provided connection string or default from configuration
                var connString = connectionString ?? _configuration.GetConnectionString("DefaultConnection");
                var logException = int.Parse(_configuration["LogError"] ?? "1");

                var result = await _helpDeskUserLandingPageServices.UpdateIsEditTicket(request, connString, logException);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating edit ticket status");
                return StatusCode(500, "An error occurred while updating edit ticket status");
            }
        }
        #endregion
    }
}
