using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PBC.UtilityService.Utilities;
using PBC.UtilityService.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PBC.UtilityService.Services
{
    /// <summary>
    /// Service implementation for Common Functionalities operations
    /// </summary>
    public class CommonFunctionalitiesService : ICommonFunctionalitiesService
    {
        private readonly ILogger<CommonFunctionalitiesService> _logger;
        private readonly IConfiguration _configuration;
        private readonly int _logException;

        public CommonFunctionalitiesService(
            ILogger<CommonFunctionalitiesService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _logException = Convert.ToInt32(_configuration["LogError"] ?? "0");
        }

        /// <inheritdoc/>
        public async Task<IActionResult> GetGlobalResourceObjectAsync(string cultureValue, string resourceKey)
        {
            _logger.LogInformation("Getting global resource object for culture: {Culture}, key: {Key}", cultureValue, resourceKey);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.GetGlobalResourceObject(cultureValue, resourceKey);
        }

        /// <inheritdoc/>
        public async Task<string> GetResourceStringAsync(string cultureValue, string resourceKey)
        {
            _logger.LogInformation("Getting resource string for culture: {Culture}, key: {Key}", cultureValue, resourceKey);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.GetResourceString(cultureValue, resourceKey);
        }

        /// <inheritdoc/>
        public async Task<IActionResult> InsertGPSDetailsAsync(int companyId, int branchId, int userId, int objectId, int recordId,
            double latitude, double longitude, string actionName, bool isFromMobile, int menuId,
            DateTime? loggedInDate = null, DateTime? loggedDateTime = null)
        {
            _logger.LogInformation("Inserting GPS details for user: {UserId}, company: {CompanyId}", userId, companyId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.InsertGPSDetails(companyId, branchId, userId, objectId, recordId,
                latitude, longitude, actionName, isFromMobile, menuId, loggedInDate, loggedDateTime);
        }

        /// <inheritdoc/>
        public async Task<DateTime> LocalTimeAsync(int branchId, DateTime serverTime)
        {
            _logger.LogInformation("Converting to local time for branch: {BranchId}", branchId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.LocalTime(branchId, serverTime);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<GNM_Company>> LoadCompanyAsync(string companyType, string connectionString)
        {
            _logger.LogInformation("Loading companies excluding type: {CompanyType}", companyType);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.LoadCompany(connectionString, _logException, companyType);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<GNM_Branch>> LoadBranchAsync(bool active, int companyId, string connectionString)
        {
            _logger.LogInformation("Loading branches for company: {CompanyId}, active only: {Active}", companyId, active);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.LoadBranch(connectionString, _logException, active, companyId);
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<GNM_BranchLocale>> LoadBranchLocaleAsync(bool active, int companyId, int userLanguageId, string connectionString)
        {
            _logger.LogInformation("Loading branch locales for company: {CompanyId}, language: {LanguageId}", companyId, userLanguageId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.LoadBranchLocale(connectionString, _logException, active, companyId, userLanguageId);
        }

        /// <inheritdoc/>
        public async Task<string> DeleteAttachmentsAsync(Attachements[] attachments, string serverPath, string connectionString)
        {
            _logger.LogInformation("Deleting {Count} attachments", attachments.Length);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.DeleteAttachments(attachments, serverPath, connectionString);
        }

        /// <inheritdoc/>
        public async Task<int> GetAttachmentCountAsync(int objectId, int transactionId, int detailId, string connectionString)
        {
            _logger.LogInformation("Getting attachment count for object: {ObjectId}, transaction: {TransactionId}", objectId, transactionId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.GetAttachmentCount(objectId, transactionId, detailId, connectionString);
        }

        /// <inheritdoc/>
        public async Task<List<Attachements>> UploadAttachmentAsync(Attachements[] attachments, int transactionId, int userId, int companyId, int detailId, string connectionString)
        {
            _logger.LogInformation("Uploading {Count} attachments for transaction: {TransactionId}", attachments.Length, transactionId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.UploadAttachment(attachments, transactionId, userId, companyId, detailId, connectionString);
        }

        /// <inheritdoc/>
        public string ConvertToHours(int minutes)
        {
            _logger.LogInformation("Converting {Minutes} minutes to hours format", minutes);
            return CommonFunctionalities.ConvertToHours(minutes);
        }

        /// <inheritdoc/>
        public async Task<string> GetGrpQinconditionAsync(int userId, string connectionString, int logException)
        {
            _logger.LogInformation("Getting workflow role condition for user: {UserId}", userId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.getGrpQincondition(userId, connectionString, logException);
        }

        /// <inheritdoc/>
        public async Task<double> GetWorkingHoursAsync(DateTime? callDate, int companyId, string connectionString, int logException)
        {
            _logger.LogInformation("Calculating working hours for company: {CompanyId}, date: {CallDate}", companyId, callDate);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.getWorkingHours(callDate, companyId, connectionString, logException);
        }

        /// <inheritdoc/>
        public async Task<int> GetObjectIDAsync(string name, string connectionString)
        {
            _logger.LogInformation("Getting object ID for name: {Name}", name);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.GetObjectID(name, connectionString, _logException);
        }

        /// <inheritdoc/>
        public async Task<bool> CheckPartySpecificAsync(int partyId, int callComplexityId, int? callPriorityId, int companyId, string connectionString, int logException)
        {
            _logger.LogInformation("Checking party-specific SLA for party: {PartyId}, company: {CompanyId}", partyId, companyId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.CheckPartySpecific(partyId, callComplexityId, callPriorityId, companyId, connectionString, logException);
        }

        /// <inheritdoc/>
        public async Task<string> GetRegionNameAsync(int userLanguageId, int generalLanguageId, int? branchId, string connectionString)
        {
            _logger.LogInformation("Getting region name for branch: {BranchId}, language: {LanguageId}", branchId, userLanguageId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.getRegionName(userLanguageId, generalLanguageId, branchId, connectionString, _logException);
        }

        /// <inheritdoc/>
        public async Task<string> GetStatusIDsAsync(int statusId, int workFlowId, string connectionString, int logException)
        {
            _logger.LogInformation("Getting workflow status IDs for status: {StatusId}, workflow: {WorkFlowId}", statusId, workFlowId);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.getStatusIDs(statusId, workFlowId, connectionString, logException);
        }

        /// <inheritdoc/>
        public async Task<bool> CheckForAdminAsync(int userId, string workFlowName, string dbName, string connectionString, int logException)
        {
            _logger.LogInformation("Checking if user {UserId} is admin for workflow: {WorkFlowName}", userId, workFlowName);

            await Task.Delay(1); // Simulate async operation
            return WorkFlowCommon.chkforAdmin(userId, workFlowName, dbName, connectionString, logException);
        }

        public async Task<IActionResult> SelAllAttachment(SelAllAttachmentList SelAllAttachmentObj, string constring, int LogException, string sidx, string sord, int page, int rows, bool _search, string filters)
        {
            _logger.LogInformation("Selecting all attachments for transaction: {TransactionID}", SelAllAttachmentObj.TransactionID);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.SelAllAttachment(SelAllAttachmentObj, constring, LogException, sidx, sord, page, rows, _search, filters);
        }

        public async Task<JsonResult> GetSRRoleAccess(string name, int UserID, string Conn)
        {
            _logger.LogInformation("Getting role access for object: {Name}", name);

            await Task.Delay(1); // Simulate async operation
            return CommonFunctionalities.GetSRRoleAccess(name, UserID, Conn);
        }
    }
}
