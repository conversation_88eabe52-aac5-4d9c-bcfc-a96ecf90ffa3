FROM mcr.microsoft.com/dotnet/sdk:8.0-windowsservercore-ltsc2022 AS builder

WORKDIR /app

COPY /PBC.UtilityService.csproj ./

RUN dotnet restore

COPY ./ ./

RUN dotnet publish -c Release -o /app/out

FROM mcr.microsoft.com/dotnet/aspnet:8.0-windowsservercore-ltsc2022 AS runner

WORKDIR /app

COPY --from=builder /app/out ./

ENV ASPNETCORE_URLS=http://+:5003

EXPOSE 5003

ENTRYPOINT ["dotnet", "PBC.UtilityService.dll"]