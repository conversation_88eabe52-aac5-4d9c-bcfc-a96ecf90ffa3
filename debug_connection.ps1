# Test the SecurityController.GetUserConnectionString method by calling a simple endpoint
Write-Host "Testing connection string retrieval..."

# First, let's test what connection string is being generated
$testPayload = @{
    objectId = 1583
    userId = 742
    connectionString = ""
    logException = 1
} | ConvertTo-Json

Write-Host "Testing UtilityService directly with empty connection string..."
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5003/api/utilities/initial-setup' -Method POST -Body $testPayload -ContentType 'application/json'
    Write-Host "UtilityService response with empty connection string:"
    $response | ConvertTo-Json -Depth 2
} catch {
    Write-Host "Error with empty connection string: $($_.Exception.Message)"
}

Write-Host "`n" + "="*50 + "`n"

# Now test with the correct connection string
$testPayloadWithConn = @{
    objectId = 1583
    userId = 742
    connectionString = "Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_AMP_MicroService_GUI; User ID=sd_dev;password=**********;multipleactiveresultsets=True;"
    logException = 1
} | ConvertTo-Json

Write-Host "Testing UtilityService directly with correct connection string..."
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5003/api/utilities/initial-setup' -Method POST -Body $testPayloadWithConn -ContentType 'application/json'
    Write-Host "UtilityService response with correct connection string:"
    $response | ConvertTo-Json -Depth 2
} catch {
    Write-Host "Error with correct connection string: $($_.Exception.Message)"
}
