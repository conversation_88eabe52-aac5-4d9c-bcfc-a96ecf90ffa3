using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PBC.HelpdeskService.Models;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace PBC.HelpdeskService.Services
{
    /// <summary>
    /// Client for communicating with PBC.UtilityService
    /// </summary>
    public class UtilityServiceClient : IUtilityServiceClient
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UtilityServiceClient> _logger;
        private readonly string _utilityServiceUrl;

        public UtilityServiceClient(HttpClient httpClient, IConfiguration configuration, ILogger<UtilityServiceClient> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            _utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";
        }

        /// <inheritdoc/>
        public async Task<string> DecryptStringAsync(string encryptedString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to decrypt string");

                var json = JsonConvert.SerializeObject(encryptedString);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/decrypt-string", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to decrypt string. Status: {StatusCode}", response.StatusCode);
                    return encryptedString; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to decrypt string");
                return encryptedString; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<JsonResult> GetInitialSetupAsync(int objectId, int userId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service for initial setup");

                var request = new InitialSetupRequest
                {
                    ObjectId = objectId,
                    UserId = userId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/initial-setup", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    // Return the raw JSON string to avoid double serialization issues
                    var data = JsonConvert.DeserializeObject<dynamic>(result);
                    return new JsonResult(data);
                }
                else
                {
                    _logger.LogWarning("Failed to get initial setup. Status: {StatusCode}", response.StatusCode);
                    return new JsonResult(new { error = "Failed to get initial setup" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service for initial setup");
                return new JsonResult(new { error = ex.Message });
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetObjectIdAsync(string name, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get object ID for: {Name}", name);

                var request = new GetObjectIdRequest
                {
                    Name = name,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/object-id", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<int>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get object ID. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get object ID");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<bool> CheckPermissionsAsync(string name, string wfName, string helpDesk, int companyId, int logException, int userId, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to check permissions for: {Name}", name);

                var request = new CheckPermissionsRequest
                {
                    Name = name,
                    WFName = wfName,
                    HelpDesk = helpDesk,
                    CompanyId = companyId,
                    LogException = logException,
                    UserId = userId,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/check-permissions", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<bool>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to check permissions. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to check permissions");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetValueFromDbAsync(string query, Dictionary<string, object> parameters, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get value from database");

                var request = new GetValueFromDbRequest
                {
                    Query = query,
                    Parameters = parameters,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-value-from-db", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get value from database. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get value from database");
                return string.Empty; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetWorkFlowIDAsync(string workFlowName, string dbName, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get workflow ID for: {WorkFlowName}", workFlowName);

                var request = new GetWorkFlowIDRequest
                {
                    WorkFlowName = workFlowName,
                    DBName = dbName,
                    ConnString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-workflow-id", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<int>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get workflow ID. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get workflow ID");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<bool> CheckIsAddRecordsAsync(int objectId, int workFlowId, int companyId, int userId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to check add records permission for user: {UserId}", userId);

                var request = new CheckAddRecordsRequest
                {
                    ObjectID = objectId,
                    WorkFlowID = workFlowId,
                    CompanyID = companyId,
                    UserID = userId,
                    ConnString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/check-is-add-records", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<bool>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to check add records permission. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to check add records permission");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<List<dynamic>> GetGroupQueueAsync(int companyId, string connectionString, int workFlowId, int userId)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get group queue for workflow: {WorkFlowId}, user: {UserId}", workFlowId, userId);

                var request = new GroupQueueAdvancedRequest
                {
                    CompanyID = companyId,
                    ConnString = connectionString,
                    WorkFlowID = workFlowId,
                    UserID = userId
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-group-queue", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<List<dynamic>>(result) ?? new List<dynamic>();
                }
                else
                {
                    _logger.LogWarning("Failed to get group queue. Status: {StatusCode}", response.StatusCode);
                    return new List<dynamic>(); // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get group queue");
                return new List<dynamic>(); // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<List<dynamic>> GetAllQueueAsync(int companyId, int workFlowId, int userId, int statusId, int branchId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get all queue for workflow: {WorkFlowId}, user: {UserId}", workFlowId, userId);

                var request = new AllQueueRequest
                {
                    CompanyID = companyId,
                    WorkFlowID = workFlowId,
                    UserID = userId,
                    ConnString = connectionString,
                    LogException = logException,
                    StatusID = statusId,
                    BranchID = branchId
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-all-queue", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<List<dynamic>>(result) ?? new List<dynamic>();
                }
                else
                {
                    _logger.LogWarning("Failed to get all queue. Status: {StatusCode}", response.StatusCode);
                    return new List<dynamic>(); // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get all queue");
                return new List<dynamic>(); // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetEndStepStatusIDAsync(int workflowId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get end step status ID for workflow: {WorkflowId}", workflowId);

                var request = new GetEndStepStatusIDRequest
                {
                    WorkflowID = workflowId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/helpdeskcommon/end-step-status-id", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<int>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get end step status ID. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get end step status ID");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<bool> CheckPartySpecificAsync(int partyId, int callComplexityId, int? callPriorityId, int companyId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to check party-specific SLA for party: {PartyId}", partyId);

                var request = new CheckPartySpecificRequest
                {
                    PartyId = partyId,
                    CallComplexityId = callComplexityId,
                    CallPriorityId = callPriorityId,
                    CompanyId = companyId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/check-party-specific", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<bool>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to check party-specific SLA. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to check party-specific SLA");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<double> GetWorkingHoursAsync(DateTime callDate, int companyId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get working hours for company: {CompanyId}, date: {CallDate}", companyId, callDate);

                var request = new GetWorkingHoursRequest
                {
                    CallDate = callDate,
                    CompanyId = companyId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/working-hours", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<double>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get working hours. Status: {StatusCode}", response.StatusCode);
                    return 0.0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get working hours");
                return 0.0; // Fallback
            }
        }


        // public async Task<List<dynamic>> CommonMethodForEmailandSMSAsync(CommonMethodForEmailandSMSList obj, string connString, int logException)
        // {
        //     try
        //     {
        //         _logger.LogInformation("Calling utility service to decrypt string");

        //         var json = JsonConvert.SerializeObject(obj);
        //         var content = new StringContent(json, Encoding.UTF8, "application/json");

              
        //         var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/CoreEmailTemplate/Common-Method-ForEmailandSMS", content);

        //         if (response.IsSuccessStatusCode)
        //         {
        //             var result = await response.Content.ReadAsStringAsync();
        //             return JsonConvert.DeserializeObject<List<dynamic>>(result) ?? new List<dynamic>();
        //         }
        //         else
        //         {
        //             _logger.LogWarning("Failed to get all queue. Status: {StatusCode}", response.StatusCode);
        //             return new List<dynamic>(); // Fallback
        //         }

        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "Error calling utility service to decrypt string");
        //         return new List<dynamic>(); // Fallback
        //     }
        // }



        public class GetEndStepStatusIDRequest
        {
            public int WorkflowID { get; set; }

            public string? ConnectionString { get; set; }

            public int LogException { get; set; } = 1;
        }

        public class CheckPartySpecificRequest
        {
            public int PartyId { get; set; }
            public int CallComplexityId { get; set; }
            public int? CallPriorityId { get; set; }
            public int CompanyId { get; set; }
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        public class GetWorkingHoursRequest
        {
            public DateTime CallDate { get; set; }
            public int CompanyId { get; set; }
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        /// <inheritdoc/>
        public async Task<bool> CheckForAdminAsync(int userId, string workFlowName, string dbName, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to check admin for user: {UserId}, workflow: {WorkFlowName}", userId, workFlowName);

                var request = new CheckForAdminRequest
                {
                    UserId = userId,
                    WorkFlowName = workFlowName,
                    DBName = dbName,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/check-for-admin", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<bool>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to check admin status. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to check admin status");
                return false; // Fallback
            }
        }

        public class CheckForAdminRequest
        {
            public int UserId { get; set; }
            public string WorkFlowName { get; set; } = string.Empty;
            public string DBName { get; set; } = string.Empty;
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        /// <inheritdoc/>
        public async Task<bool> CheckAutoAllocationAsync(int companyId, int workFlowId, int userId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to check auto allocation for company: {CompanyId}, workflow: {WorkFlowId}, user: {UserId}", companyId, workFlowId, userId);

                var request = new CheckAutoAllocationRequest
                {
                    CompanyID = companyId,
                    WorkFlowID = workFlowId,
                    UserID = userId,
                    ConnString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/check-auto-allocation", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<bool>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to check auto allocation. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to check auto allocation");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<object> GetAutoAllocationStepDetailsAsync(int workFlowId, int companyId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get auto allocation step details for workflow: {WorkFlowId}, company: {CompanyId}", workFlowId, companyId);

                var request = new GetAutoAllocationStepDetailsRequest
                {
                    WorkFlowID = workFlowId,
                    CompanyID = companyId,
                    ConnString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-auto-allocation-step-details", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<object>(result) ?? new object();
                }
                else
                {
                    _logger.LogWarning("Failed to get auto allocation step details. Status: {StatusCode}", response.StatusCode);
                    return new object(); // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get auto allocation step details");
                return new object(); // Fallback
            }
        }

        public class CheckAutoAllocationRequest
        {
            public int CompanyID { get; set; }
            public int WorkFlowID { get; set; }
            public int UserID { get; set; }
            public string ConnString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        public class GetAutoAllocationStepDetailsRequest
        {
            public int WorkFlowID { get; set; }
            public int CompanyID { get; set; }
            public string ConnString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        /// <inheritdoc/>
        public async Task<string> GetGrpQinconditionAsync(int userId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get workflow role condition for user: {UserId}", userId);

                var request = new GetGrpQinconditionRequest
                {
                    UserId = userId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/workflow-role-condition", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to get workflow role condition. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get workflow role condition");
                return string.Empty; // Fallback
            }
        }

        public class GetGrpQinconditionRequest
        {
            public int UserId { get; set; }
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        /// <inheritdoc/>
        public async Task<string> GetStatusIDsAsync(int statusId, int workFlowId, string connectionString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get status IDs for status: {StatusId}, workflow: {WorkFlowId}", statusId, workFlowId);

                var request = new GetStatusIDsRequest
                {
                    StatusId = statusId,
                    WorkFlowId = workFlowId,
                    ConnectionString = connectionString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/status-ids", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to get status IDs. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get status IDs");
                return string.Empty; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetServiceRequestQueryAsync(string connectionString, int logException, int langID,
            string genLangCode, string userLangCode, int mode, int userId, int companyId, int branchId,
            string sidx, string sord, string dbName)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get service request query for mode: {Mode}, user: {UserId}", mode, userId);

                var request = new GetServiceRequestQueryRequest
                {
                    ConnectionString = connectionString,
                    LogException = logException,
                    LangID = langID,
                    GenLangCode = genLangCode,
                    UserLangCode = userLangCode,
                    Mode = mode,
                    User_ID = userId,
                    Company_ID = companyId,
                    Branch_ID = branchId,
                    Sidx = sidx,
                    Sord = sord,
                    DbName = dbName
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/helpdeskservicerequestapi/service-request-query", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to get service request query. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get service request query");
                return string.Empty; // Fallback
            }
        }

        public class GetServiceRequestQueryRequest
        {
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
            public int LangID { get; set; }
            public string GenLangCode { get; set; } = "en";
            public string UserLangCode { get; set; } = "en";
            public int Mode { get; set; } = 0;
            public int User_ID { get; set; } = 0;
            public int Company_ID { get; set; } = 0;
            public int Branch_ID { get; set; } = 0;
            public string Sidx { get; set; } = "";
            public string Sord { get; set; } = "";
            public string DbName { get; set; } = "";
        }

        public class GetStatusIDsRequest
        {
            public int StatusId { get; set; }
            public int WorkFlowId { get; set; }
            public string ConnectionString { get; set; } = string.Empty;
            public int LogException { get; set; } = 1;
        }

        /// <inheritdoc/>
        public async Task<string> LockRecordAsync(string connString, int logException, string userCulture, int quotationID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0)
        {
            try
            {
                _logger.LogInformation("Calling utility service to lock record for quotation: {QuotationID}, user: {UserID}, workflow: {WorkFlowName}", quotationID, userID, workFlowName);

                var request = new LockRecordRequest
                {
                    ConnString = connString,
                    LogException = logException,
                    UserCulture = userCulture,
                    QuotationID = quotationID,
                    UserID = userID,
                    CompanyID = companyID,
                    WorkFlowName = workFlowName,
                    DBName = dbName,
                    Branch_ID = branchID
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/lock-record", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? "ErrorOccuredwhileLocking";
                }
                else
                {
                    _logger.LogWarning("Failed to lock record. Status: {StatusCode}", response.StatusCode);
                    return "ErrorOccuredwhileLocking"; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to lock record");
                return "ErrorOccuredwhileLocking"; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<string> UnLockRecordAsync(string connString, int logException, string userCulture, int jobcardID, int userID, int companyID, string workFlowName, string dbName, int branchID = 0)
        {
            try
            {
                _logger.LogInformation("Calling utility service to unlock record for jobcard: {JobcardID}, user: {UserID}, workflow: {WorkFlowName}", jobcardID, userID, workFlowName);

                var request = new UnLockRecordRequest
                {
                    ConnString = connString,
                    LogException = logException,
                    UserCulture = userCulture,
                    JobcardID = jobcardID,
                    UserID = userID,
                    CompanyID = companyID,
                    WorkFlowName = workFlowName,
                    DBName = dbName,
                    Branch_ID = branchID
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/unlock-record", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? "ErrorOccuredwhileUnLocking";
                }
                else
                {
                    _logger.LogWarning("Failed to unlock record. Status: {StatusCode}", response.StatusCode);
                    return "ErrorOccuredwhileUnLocking"; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to unlock record");
                return "ErrorOccuredwhileUnLocking"; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<DateTime> LocalTimeBasedOnBranchAsync(int branchID, DateTime serverTime, string connString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to convert server time to local time for branch: {BranchID}, server time: {ServerTime}", branchID, serverTime);

                var request = new LocalTimeBasedOnBranchRequest
                {
                    BranchID = branchID,
                    ServerTime = serverTime,
                    ConnString = connString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/local-time-based-on-branch", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<DateTime>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to convert server time to local time. Status: {StatusCode}", response.StatusCode);
                    return serverTime; // Fallback to server time
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to convert server time to local time");
                return serverTime; // Fallback to server time
            }
        }

        /// <inheritdoc/>
        public async Task<DateTime> LocalTimeAsync(int userID, DateTime serverTime, string connString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to convert server time to local time for user: {UserID}, server time: {ServerTime}", userID, serverTime);

                var request = new LocalTimeRequest
                {
                    UserID = userID,
                    ServerTime = serverTime,
                    ConnString = connString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/local-time", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<DateTime>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to convert server time to local time for user. Status: {StatusCode}", response.StatusCode);
                    return serverTime; // Fallback to server time
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to convert server time to local time for user");
                return serverTime; // Fallback to server time
            }
        }

        /// <inheritdoc/>
        public async Task<string> GetEndStepStatusNameAsync(int workflowID, string connString, int logException)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get end step status name for workflow: {WorkflowID}", workflowID);

                var request = new GetEndStepStatusNameRequest
                {
                    WorkflowID = workflowID,
                    ConnString = connString,
                    LogException = logException
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/utilities/get-end-step-status-name", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to get end step status name. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get end step status name");
                return string.Empty; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object[]>> OrderByFieldAsync(string sortField, string sortDirection, object[] data)
        {
            try
            {
                _logger.LogInformation("Calling utility service to order data by field: {SortField}, direction: {SortDirection}", sortField, sortDirection);

                var request = new OrderByFieldRequest
                {
                    SortField = sortField,
                    SortDirection = sortDirection,
                    Data = data
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/extensionmethods/order-by-field", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<ExtensionMethodsResponse<object[]>>(result) ?? new ExtensionMethodsResponse<object[]>
                    {
                        Success = false,
                        Data = Array.Empty<object>(),
                        TotalCount = 0,
                        Message = "Failed to deserialize response"
                    };
                }
                else
                {
                    _logger.LogWarning("Failed to order data by field. Status: {StatusCode}", response.StatusCode);
                    return new ExtensionMethodsResponse<object[]>
                    {
                        Success = false,
                        Data = data, // Return original data as fallback
                        TotalCount = data.Length,
                        Message = $"HTTP Error: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to order data by field");
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = false,
                    Data = data, // Return original data as fallback
                    TotalCount = data.Length,
                    Message = ex.Message
                };
            }
        }

        /// <inheritdoc/>
        public async Task<ExtensionMethodsResponse<object[]>> FilterSearchAsync(Filters filters, object[] data)
        {
            try
            {
                _logger.LogInformation("Calling utility service to filter data with {RuleCount} filter rules", filters.rules.Count);

                var request = new FilterSearchRequest
                {
                    Filters = filters,
                    Data = data
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/extensionmethods/filter-search", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<ExtensionMethodsResponse<object[]>>(result) ?? new ExtensionMethodsResponse<object[]>
                    {
                        Success = false,
                        Data = Array.Empty<object>(),
                        TotalCount = 0,
                        Message = "Failed to deserialize response"
                    };
                }
                else
                {
                    _logger.LogWarning("Failed to filter data. Status: {StatusCode}", response.StatusCode);
                    return new ExtensionMethodsResponse<object[]>
                    {
                        Success = false,
                        Data = data, // Return original data as fallback
                        TotalCount = data.Length,
                        Message = $"HTTP Error: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to filter data");
                return new ExtensionMethodsResponse<object[]>
                {
                    Success = false,
                    Data = data, // Return original data as fallback
                    TotalCount = data.Length,
                    Message = ex.Message
                };
            }
        }

        // WorkflowService functions

        /// <inheritdoc/>
        public async Task<bool> CheckPrefixSuffixAsync(int companyID, int branchID, string objectName, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling workflow service to check prefix/suffix for Company: {CompanyID}, Branch: {BranchID}, Object: {ObjectName}",
                    companyID, branchID, objectName);

                var workflowServiceUrl = _configuration["ServiceUrls:WorkflowService"] ?? "http://localhost:5005";

                var request = new CheckPrefixSuffixWorkflowRequest
                {
                    CompanyID = companyID,
                    BranchID = branchID,
                    ObjectName = objectName,
                    DbName = dbName,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{workflowServiceUrl}/api/workflowapi/check-prefix-suffix", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<CheckPrefixSuffixWorkflowResponse>(result);
                    return responseObj?.HasPrefixSuffix ?? false;
                }
                else
                {
                    _logger.LogWarning("Failed to check prefix/suffix via workflow service. Status: {StatusCode}", response.StatusCode);
                    return false; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling workflow service to check prefix/suffix");
                return false; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetWorkflowObjectIDAsync(string name, string dbName, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling workflow service to get object ID for Name: {Name}, DB: {DbName}", name, dbName);

                var workflowServiceUrl = _configuration["ServiceUrls:WorkflowService"] ?? "http://localhost:5005";

                var request = new GetObjectIDWorkflowRequest
                {
                    Name = name,
                    DbName = dbName,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{workflowServiceUrl}/api/workflowapi/get-object-id", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<GetObjectIDWorkflowResponse>(result);
                    return responseObj?.ObjectID ?? 0;
                }
                else
                {
                    _logger.LogWarning("Failed to get object ID via workflow service. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling workflow service to get object ID");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<int> GetAttachmentCountAsync(int objectId, int transactionId, int detailId, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to get attachment count for object: {ObjectId}, transaction: {TransactionId}", objectId, transactionId);

                var request = new GetAttachmentCountRequest
                {
                    ObjectID = objectId,
                    TransactionID = transactionId,
                    DetailID = detailId,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/attachment-count", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<int>(result);
                }
                else
                {
                    _logger.LogWarning("Failed to get attachment count. Status: {StatusCode}", response.StatusCode);
                    return 0; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to get attachment count");
                return 0; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<string> ConvertToHoursAsync(int minutes)
        {
            try
            {
                _logger.LogInformation("Calling utility service to convert {Minutes} minutes to hours", minutes);

                var request = new ConvertToHoursRequest
                {
                    Minutes = minutes
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/convert-to-hours", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to convert minutes to hours. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to convert minutes to hours");
                return string.Empty; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<string> DeleteAttachmentsAsync(Attachements[] attachments, string serverPath, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to delete {Count} attachments", attachments.Length);

                var request = new DeleteAttachmentsRequest
                {
                    Attachments = attachments,
                    ServerPath = serverPath,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/delete-attachments", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<string>(result) ?? string.Empty;
                }
                else
                {
                    _logger.LogWarning("Failed to delete attachments. Status: {StatusCode}", response.StatusCode);
                    return string.Empty; // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to delete attachments");
                return string.Empty; // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<List<Attachements>> UploadAttachmentAsync(Attachements[] attachments, int transactionId, int userId, int companyId, int detailId, string connectionString)
        {
            try
            {
                _logger.LogInformation("Calling utility service to upload {Count} attachments for transaction: {TransactionId}", attachments.Length, transactionId);

                var request = new UploadAttachmentRequest
                {
                    Attachments = attachments,
                    TransactionId = transactionId,
                    UserId = userId,
                    CompanyId = companyId,
                    DetailId = detailId,
                    ConnectionString = connectionString
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/commonfunctionalities/upload-attachment", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<List<Attachements>>(result) ?? new List<Attachements>();
                }
                else
                {
                    _logger.LogWarning("Failed to upload attachments. Status: {StatusCode}", response.StatusCode);
                    return new List<Attachements>(); // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service to upload attachments");
                return new List<Attachements>(); // Fallback
            }
        }

        /// <inheritdoc/>
        public async Task<List<T>> AdvanceSearchAsync<T>(AdvanceFilter advanceFilter)
        {
            try
            {
                _logger.LogInformation("Calling utility service for advance search with rules");

                var request = new AdvanceSearchRequest
                {
                    AdvanceFilter = advanceFilter,
                    Data = new object[] { } // Empty data array as placeholder
                };

                var json = JsonConvert.SerializeObject(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_utilityServiceUrl}/api/extensionmethods/advance-search", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonConvert.DeserializeObject<ExtensionMethodsResponse<object[]>>(result);

                    if (responseObj?.Success == true && responseObj.Data != null)
                    {
                        // Convert the object array to the requested type
                        var convertedData = new List<T>();
                        foreach (var item in responseObj.Data)
                        {
                            if (item is T typedItem)
                            {
                                convertedData.Add(typedItem);
                            }
                            else
                            {
                                // Try to convert using JSON serialization/deserialization
                                var itemJson = JsonConvert.SerializeObject(item);
                                var convertedItem = JsonConvert.DeserializeObject<T>(itemJson);
                                if (convertedItem != null)
                                {
                                    convertedData.Add(convertedItem);
                                }
                            }
                        }
                        return convertedData;
                    }
                    else
                    {
                        _logger.LogWarning("Advance search returned unsuccessful response");
                        return new List<T>();
                    }
                }
                else
                {
                    _logger.LogWarning("Failed to perform advance search. Status: {StatusCode}", response.StatusCode);
                    return new List<T>(); // Fallback
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling utility service for advance search");
                return new List<T>(); // Fallback
            }
        }

        // Request models for UtilityService communication
        public class GetAttachmentCountRequest
        {
            public int ObjectID { get; set; }
            public int TransactionID { get; set; }
            public int DetailID { get; set; }
            public string ConnectionString { get; set; }
        }

        public class ConvertToHoursRequest
        {
            public int Minutes { get; set; }
        }

        public class DeleteAttachmentsRequest
        {
            public Attachements[] Attachments { get; set; }
            public string ServerPath { get; set; }
            public string ConnectionString { get; set; }
        }

        public class UploadAttachmentRequest
        {
            public Attachements[] Attachments { get; set; }
            public int TransactionId { get; set; }
            public int UserId { get; set; }
            public int CompanyId { get; set; }
            public int DetailId { get; set; }
            public string ConnectionString { get; set; }
        }

        // Request/Response models for WorkflowService communication
        public class CheckPrefixSuffixWorkflowRequest
        {
            public int CompanyID { get; set; }
            public int BranchID { get; set; }
            public string ObjectName { get; set; } = string.Empty;
            public string DbName { get; set; } = string.Empty;
            public string ConnectionString { get; set; } = string.Empty;
        }

        public class CheckPrefixSuffixWorkflowResponse
        {
            public bool Success { get; set; }
            public bool HasPrefixSuffix { get; set; }
            public string Message { get; set; } = string.Empty;
        }

        public class GetObjectIDWorkflowRequest
        {
            public string Name { get; set; } = string.Empty;
            public string DbName { get; set; } = string.Empty;
            public string ConnectionString { get; set; } = string.Empty;
        }

        public class GetObjectIDWorkflowResponse
        {
            public bool Success { get; set; }
            public int ObjectID { get; set; }
            public string Message { get; set; } = string.Empty;
        }

        public class LocalTimeRequest
        {
            public int UserID { get; set; }
            public DateTime ServerTime { get; set; }
            public string ConnString { get; set; } = string.Empty;
        }
    }
}
