$body = '{
  "objectId": 1583,
  "user_ID": 742,
  "helpDesk": "",
  "company_ID": 1,
  "needToChangepassword": false
}'

Write-Host "Testing InitialSetup endpoint..."
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5004/api/Helpdesk/InitialSetup' -Method POST -Body $body -ContentType 'application/json' -Verbose
    Write-Host "Response received successfully"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
