﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Data;
using System.Data.SqlClient;

namespace PBC.AggregatorService.Controllers
{
    
    [ApiController]
    [Route("api/[controller]")]
    public class SecurityController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly List<ApiClient> _allowedClients;


        public SecurityController(IConfiguration configuration)
        {
            _configuration = configuration;
            _allowedClients = _configuration.GetSection("AllowedClients").Get<List<ApiClient>>();

        }

        // [HttpPost("JWTGenOnLogin")]
        // [AllowAnonymous]
        // public IActionResult JWTGenOnLogin([FromBody] UserLoginModel model)
        // {
        //     var token = GenerateToken(model.Username);
        //     return Ok(new { token });
        // }

        [HttpPost("JWTGenOnLogin")]
        [AllowAnonymous]
        public IActionResult JWTGenOnLogin()
        {
            if (!Request.Headers.TryGetValue("X-API-Key", out var receivedApiKeyHeader))
            {
                return Unauthorized(new { message = "API Key header is missing." });
            }
            var receivedApiKey = receivedApiKeyHeader.ToString();

            var client = FindClientByApiKey(receivedApiKey);

            if (client == null)
            {
                return Unauthorized(new { message = "Invalid API Key." });
            }

            //if (!AuthenticateUser(model.Username, model.Password))
            //{
            //    return Unauthorized(new { message = "Invalid User" });
            //}

            try
            {
                var token = GenerateToken(client.ClientName);
                return Ok(new { token });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "An unexpected error occurred." });
            }
        }


        private bool AuthenticateUser(string username, string password)
        {
            var allowedClients = _configuration.GetSection("AllowedClients").Get<List<ApiClient>>();

            // You can add logic to select by ClientName or ApiKey if needed
            var client = allowedClients.FirstOrDefault(c => c.UserName == username && c.Password == password);

            return client != null;
        }


        private ApiClient FindClientByApiKey(string apiKey)
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                return null;
            }

            var receivedApiKeyBytes = Encoding.UTF8.GetBytes(apiKey);

            foreach (var client in _allowedClients)
            {
                var expectedApiKeyBytes = Encoding.UTF8.GetBytes(client.ApiKey);
                if (CryptographicOperations.FixedTimeEquals(receivedApiKeyBytes, expectedApiKeyBytes))
                {
                    return client;
                }
            }

            return null;
        }

        /// <summary>
        /// Generate JWT token
        /// </summary>
        /// <param name="username"></param>
        /// <returns></returns>
        private string GenerateToken(string username)
        {
            var secretKey = _configuration["Jwt:SecretKey"];
            var issuer = _configuration["Jwt:Issuer"];
            var audience = _configuration["Jwt:Audience"];
            var expiryMinutes = int.Parse(_configuration["Jwt:ExpiryMinutes"]);

            var securityKey = new SymmetricSecurityKey(Convert.FromBase64String(secretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, username ?? ""),
                new Claim(JwtRegisteredClaimNames.Iss, issuer),
                new Claim(JwtRegisteredClaimNames.Aud, audience)
            };

            var now = DateTime.UtcNow;
            var expires = now.AddMinutes(expiryMinutes);

            var token = new JwtSecurityToken(
                issuer: issuer,
                audience: audience,
                claims: claims,
                notBefore: now,
                expires: expires,
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }




        public static string GetUserConnectionString(string userID, int type, string usermail)
        {
            string customerDbConnectionString = string.Empty;            
            string centralDbConnectionString = "Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCL_Aftermarket_PBC_CentralDB; User ID=sd_dev;Password=**********;MultipleActiveResultSets=True;";
            try
            {               
                using (SqlConnection sqlCon = new SqlConnection(centralDbConnectionString))
                {
                    if (sqlCon.State == ConnectionState.Closed || sqlCon.State == ConnectionState.Broken)
                    {
                        sqlCon.Open();
                    }

                    DataTable dt = new DataTable();
                    using (SqlCommand sqlComm = new SqlCommand(
                        @"SELECT * FROM MA_HCLProductsUsers WHERE  User_Login_ID = @userID AND Email = @Email", sqlCon))  //User_Login_ID = @userID AND
                    {
                        sqlComm.Parameters.AddWithValue("@userID", "quest_admin");
                        sqlComm.Parameters.AddWithValue("@Email", "<EMAIL>");
                        dt.Load(sqlComm.ExecuteReader());

                        if (dt != null && dt.Rows.Count > 0)
                        {
                            object value = dt.Rows[0]["CustomerDBConnectionString"];
                            if (value != null && value != DBNull.Value)
                            {
                                customerDbConnectionString = value.ToString();
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                // Log the exception as needed
                // Example: SharedAPIClassLibrary_DC.Utilities.ExceptionLogger.ErrorLog(e, null, Convert.ToInt32(userID), "");
            }

            return customerDbConnectionString;
        }


    }




    public class UserLoginModel
    {
        public string Username { get; set; }
        public string Password { get; set; }
        // Add other properties as needed
    }
    public class ApiClient
    {
        public string ClientName { get; set; }
        public string ApiKey { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
    }
}