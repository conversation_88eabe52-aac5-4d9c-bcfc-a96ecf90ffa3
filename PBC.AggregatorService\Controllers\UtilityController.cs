using Microsoft.AspNetCore.Mvc;
using PBC.AggregatorService.Services;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Authorization;
using System.Text;
using PBC.AggregatorService.DTOs;


namespace PBC.AggregatorService.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UtilityController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<UtilityController> _logger;
        private readonly HttpClient _httpClient;

        public UtilityController(IConfiguration configuration, ILogger<UtilityController> logger, HttpClient httpClient)
        {
            _configuration = configuration;
            _logger = logger;
            _httpClient = httpClient;
        }

        /// <summary>
        /// Get SR Role Access
        /// </summary>
        /// <param name="request">Get SR Role Access request</param>
        /// <returns>SR Role Access data</returns>
        [HttpPost("get-sr-role-access")]
        [Authorize]
        public async Task<IActionResult> GetSRRoleAccess([FromBody] GetSRRoleAccessRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/Utility/get-sr-role-access");

                // Get connection string and log exception setting from configuration
                //string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;

                string connString = SecurityController.GetUserConnectionString(
                "<EMAIL>",
                1, // or type - admin super user , rol user
                "<EMAIL>" // or request.Email if that's the property name
                );

                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.UtilityService
                var utilityServiceUrl = _configuration["ServiceUrls:UtilityService"];
                var endpoint = $"{utilityServiceUrl}/api/commonfunctionalities/get-sr-role-access";

                var jsonContent = JsonConvert.SerializeObject(new { name = request.Name, UserID = request.UserID, Conn = connString });
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // Return the successful response content
                    return Ok(responseContent);
                }
                else
                {
                    // Log and return error response
                    _logger.LogError("Failed to get SR Role Access: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred in GetSRRoleAccess");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }
        
        [HttpPost("sel-all-attachment")]
        [Authorize]
        public async Task<IActionResult> SelAllAttachment([FromBody] SelAllAttachmentRequest request)
        {
            try
            {
                _logger.LogInformation("POST /api/commonfunctionalities/sel-all-attachment");

                // Get connection string and log exception setting from configuration
                //string connString = _configuration.GetConnectionString("FSMGOLD") ?? string.Empty;

                string connString = SecurityController.GetUserConnectionString(
                "<EMAIL>",
                1, // or type - admin super user , rol user
                "<EMAIL>" // or request.Email if that's the property name
                );

                int logException = Convert.ToInt32(_configuration["LogError"] ?? "1");

                // Call PBC.UtilityService
                var utilityServiceUrl = _configuration["ServiceUrls:UtilityService"];
                var endpoint = $"{utilityServiceUrl}/api/commonfunctionalities/sel-all-attachment";

                var jsonContent = JsonConvert.SerializeObject(new { SelAllAttachmentObj = request.SelAllAttachmentObj, constring = connString, LogException = logException, sidx = request.Sidx, sord = request.Sord, page = request.Page, rows = request.Rows, _search = request._search, filters = request.Filters });
                var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{endpoint}", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    // Return the successful response content
                    return Ok(responseContent);
                }
                else
                {
                    // Log and return error response
                    _logger.LogError("Failed to get all attachments: {StatusCode} - {Content}", response.StatusCode, responseContent);
                    return StatusCode((int)response.StatusCode, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred in SelAllAttachment");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }
    }
}
