# Test what connection string the SecurityController is generating
Write-Host "Testing SecurityController connection string generation..."

# Let's create a simple test endpoint to see what connection string is being generated
# We'll modify the InitialSetup to log the connection string

$body = @{
    objectId = 1583
    user_ID = 742
    helpDesk = ""
    company_ID = 1
    needToChangepassword = $false
} | ConvertTo-Json

Write-Host "Calling InitialSetup to see what connection string is being used..."
Write-Host "Request body: $body"

try {
    $response = Invoke-WebRequest -Uri 'http://localhost:5004/api/Helpdesk/InitialSetup' -Method POST -Body $body -ContentType 'application/json' -Verbose
    Write-Host "Status Code: $($response.StatusCode)"
    Write-Host "Response Headers:"
    $response.Headers | Format-Table
    Write-Host "Response Content:"
    $response.Content | ConvertFrom-Json | ConvertTo-Json -Depth 2
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Response Status: $($_.Exception.Response.StatusCode)"
        Write-Host "Response Content: $($_.Exception.Response | Get-Member)"
    }
}
