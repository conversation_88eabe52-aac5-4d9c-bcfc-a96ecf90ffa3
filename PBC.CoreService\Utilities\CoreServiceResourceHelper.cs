using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using System.Resources;

namespace PBC.CoreService.Utilities
{
    /// <summary>
    /// Defines methods for accessing global resources in the CoreService.
    /// </summary>
    public interface ICoreServiceResourceHelper
    {
        /// <summary>
        /// Retrieves a global resource object as an IActionResult.
        /// </summary>
        /// <param name="cultureValue">The culture identifier.</param>
        /// <param name="resourceKey">The key of the resource to retrieve.</param>
        /// <param name="assembly">The assembly containing the resource. Optional.</param>
        /// <returns>An IActionResult containing the resource value.</returns>
        IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly? assembly = null);

        /// <summary>
        /// Retrieves a global resource string.
        /// </summary>
        /// <param name="cultureValue">The culture identifier.</param>
        /// <param name="resourceKey">The key of the resource to retrieve.</param>
        /// <returns>The resource string value.</returns>
        string GetResourceString(string cultureValue, string resourceKey);
    }

    /// <summary>
    /// Provides helper methods for accessing global resources in the CoreService.
    /// </summary>
        public class CoreServiceResourceHelper : ICoreServiceResourceHelper
    {
        private readonly ILogger<CoreServiceResourceHelper> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CoreServiceResourceHelper"/> class.
        /// </summary>
        /// <param name="logger">The logger instance for logging errors and information.</param>
        public CoreServiceResourceHelper(ILogger<CoreServiceResourceHelper> logger)
        {
            _logger = logger;
        }
        #region :::GetGlobalResourceObject   Puneeth Y 03-07-2025 :::
        /// <summary>
        /// To Get GlobalResourceObject
        /// </summary>
        /// 
        public IActionResult GetGlobalResourceObject(string cultureValue, string resourceKey, Assembly? assembly = null)
        {
            try
            {
                if (assembly == null)
                {
                    assembly = Assembly.GetExecutingAssembly();
                }

                string cultureIdentifier = cultureValue.Replace("Resource_", "");
                string resourceNamespace = assembly.GetName().Name + ".App_GlobalResources.";
                string resourceFileName = "resource_" + cultureIdentifier.ToLowerInvariant();
                ResourceManager resourceManager = new ResourceManager(resourceNamespace + resourceFileName, assembly);
                string? resourceValue = resourceManager.GetString(resourceKey);

                return new JsonResult(resourceValue);
            }
            catch (Exception ex)
            {
                // Console.WriteLine($"Error accessing resource: {ex.Message}");
                _logger.LogError(ex, "Error accessing resource: {Message}", ex.Message);
                return new JsonResult(string.Empty);
            }
        }
        public string GetResourceString(string cultureValue, string resourceKey)
        {
            var actionResult = GetGlobalResourceObject(cultureValue, resourceKey);
            if (actionResult is JsonResult jsonResult)
            {
                return jsonResult.Value?.ToString() ?? string.Empty;
            }
            return string.Empty;
        }
        #endregion
    }
}