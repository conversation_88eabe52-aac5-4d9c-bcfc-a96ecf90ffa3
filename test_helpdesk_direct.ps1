# Test the HelpdeskService directly
Write-Host "Testing HelpdeskService initial-setup endpoint directly..."

$body = @{
    objectId = 1583
    user_ID = 742
    helpDesk = ""
    company_ID = 1
    needToChangepassword = $false
} | ConvertTo-Json

$connString = "Data Source=quest-dev-rds-enc.cew1ojzgpj8q.ap-south-1.rds.amazonaws.com; Initial Catalog=HCLSoftware_AMP_MicroService_GUI; User ID=sd_dev;password=**********;multipleactiveresultsets=True;"

Write-Host "Calling HelpdeskService directly with connection string parameter..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/helpdeskuserlandingpage/initial-setup?connectionString=$([System.Web.HttpUtility]::UrlEncode($connString))" -Method POST -Body $body -ContentType 'application/json'
    Write-Host "HelpdeskService response:"
    $response | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        Write-Host "Response Status: $($_.Exception.Response.StatusCode)"
    }
}
