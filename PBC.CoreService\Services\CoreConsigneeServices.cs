using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using System.Text;
using System.Text.Json;
using PBC.CoreService.Utilities.DTOs;
using PBC.CoreService.Utilities;


namespace PBC.CoreService.Services
{
    public class CoreConsigneeServices : ICoreConsigneeServices
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CoreConsigneeServices> _logger;
        private readonly IConfiguration _configuration;
        private readonly ICoreServiceResourceHelper _coreServiceResourceHelper;

        public CoreConsigneeServices(HttpClient httpClient, ILogger<CoreConsigneeServices> logger, IConfiguration configuration, ICoreServiceResourceHelper coreServiceResourceHelper)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _coreServiceResourceHelper=coreServiceResourceHelper;
        }

        /// <summary>
        /// Decrypt string using PBC.UtilityService
        /// </summary>
        private async Task<string> DecryptStringAsync(string encryptedString)
        {
            try
            {
                var request = new
                {
                    EncryptedString = encryptedString
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/decrypt-string", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<string>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? string.Empty : string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for DecryptString");
                return string.Empty;
            }
        }

        /// <summary>
        /// Filter search using PBC.UtilityService
        /// </summary>
        private async Task<T[]> FilterSearchAsync<T>(T[] data, object filters)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    Filters = filters
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/filter-search", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for FilterSearch");
                return data;
            }
        }

        /// <summary>
        /// Advance search using PBC.UtilityService
        /// </summary>
        private async Task<T[]> AdvanceSearchAsync<T>(T[] data, object advanceFilter)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    AdvanceFilter = advanceFilter
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/advance-search", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for AdvanceSearch");
                return data;
            }
        }

        /// <summary>
        /// Order by field using PBC.UtilityService
        /// </summary>
        private async Task<T[]> OrderByFieldAsync<T>(T[] data, string sortField, string sortDirection)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    SortField = sortField,
                    SortDirection = sortDirection
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/order-by-field", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for OrderByField");
                return data;
            }
        }

        /// <summary>
        /// Paginate data using PBC.UtilityService
        /// </summary>
        private async Task<T[]> PaginateAsync<T>(T[] data, int page, int rows)
        {
            try
            {
                var request = new
                {
                    Data = data,
                    Page = page,
                    Rows = rows
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/extensionmethods/paginate", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<ExtensionMethodsResponse<T[]>>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                    return result?.Success == true ? result.Data ?? data : data;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error calling PBC.UtilityService for Paginate");
                return data;
            }
        }

        /// <summary>
        /// Log to PBC.UtilityService
        /// </summary>
        private async Task LogToUtilityServiceAsync(int exId, string exMessage, string exDetails, string exStackTrace)
        {
            try
            {
                var request = new
                {
                    ExId = exId,
                    ExMessage = exMessage,
                    ExDetails = exDetails,
                    ExStackTrace = exStackTrace
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                await _httpClient.PostAsync($"{utilityServiceUrl}/api/logsheetexporter/log", content, cts.Token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling PBC.UtilityService for logging");
            }
        }

        #region::: LoadBranchDropdown /Vinay:::
        /// <summary>
        /// LoadBranchDropdown
        /// </summary>
        /// <param name="LoadBranchDropdownObj"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> LoadBranchDropdown(LoadBranchDropdownList LoadBranchDropdownObj, string constring, int LogException)
        {
            var jsonData = default(dynamic);

            try
            {

                int UserLang = Convert.ToInt32(LoadBranchDropdownObj.UserLanguageID);
                int GenLang = Convert.ToInt32(LoadBranchDropdownObj.GeneralLanguageID);
                //int GenLang = 10;
                int branchID = Convert.ToInt32(LoadBranchDropdownObj.Branch);

                //IEnumerable<GNM_Branch> BranchObject = CompanyClient.GNM_Branch.Where(a => a.Company_ID == UserDetails.Company_ID && a.Branch_Active == true);
                List<Branchs> BranchObject = new List<Branchs>();
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    string query = "USP_GetBranchDetails";



                    try
                    {
                        using (SqlCommand command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;
                            command.Parameters.AddWithValue("@CompanyID", LoadBranchDropdownObj.Company_ID);
                            command.Parameters.AddWithValue("@UserLang", UserLang);
                            command.Parameters.AddWithValue("@GenLang", GenLang);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    Branchs branch = new Branchs
                                    {
                                        ID = reader.GetInt32(reader.GetOrdinal("ID")),
                                        Name = reader.GetString(reader.GetOrdinal("Name"))
                                    };
                                    BranchObject.Add(branch);
                                }
                            }
                        }
                    }
                    catch
                    {

                    }
                }
                jsonData = new
                {
                    BranchArr = BranchObject.OrderBy(b => b.Name),
                    branchID
                };

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            //return Json(jsonData, JsonRequestBehavior.AllowGet);
            return new JsonResult(jsonData);
        }
        #endregion

        #region::: Select Vinay N 19/8/24
        /// <summary>
        /// 
        /// </summary>
        /// <param name="connString"></param>
        /// <param name="SelectConsigneeObj"></param>
        /// <param name="sidx"></param>
        /// <param name="rows"></param>
        /// <param name="page"></param>
        /// <param name="sord"></param>
        /// <param name="_search"></param>
        /// <param name="nd"></param>
        /// <param name="filters"></param>
        /// <param name="advnce"></param>
        /// <param name="Query"></param>
        /// <returns></returns>



        public async Task<IActionResult> Select(string connString, GetAllconsigneeList SelectConsigneeObj, string sidx, int rows, int page, string sord, bool _search, long nd, string filters, bool advnce, string advanceFilter)
        {
            int LogException = 1; // Default to logging enabled
            var jsonData = default(dynamic);
            try
            {
                int Count = 0;
                int Total = 0;
                string AppPath = string.Empty;

                int Company_ID = Convert.ToInt32(SelectConsigneeObj.Company_ID);


                //ConsigneeClient = new ConsigneeEntities();
                //IEnumerable<GNM_Consignee> IEConsigneeList = ConsigneeClient.GNM_Consignee.Where(i => i.Branch_ID == Branch_ID);
                //IEnumerable<GNM_ConsigneeLocale> IEConsigneeLocaleList = ConsigneeClient.GNM_ConsigneeLocale.Where(i => i.GNM_Consignee.Branch_ID == Branch_ID && i.Language_ID == LanguageID);
                IQueryable<ConsigneeMaster> IQConsigneeMaster = null;
                //IEnumerable<ConsigneeMaster> IEConsigneeMasterArray = null;
                //string YesE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "Yes").ToString();
                //string NoE = HttpContext.GetGlobalResourceObject(Session["GeneralCulture"].ToString(), "No").ToString();
                //string YesL = HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "Yes").ToString();
                //string NoL = HttpContext.GetGlobalResourceObject(Session["UserCulture"].ToString(), "No").ToString();



                List<GNM_WareHouse> WareHouseList = new List<GNM_WareHouse>();

                List<GNM_WareHouse> WareHouseList2 = new List<GNM_WareHouse>();
                var WareHouseLocaleArray = new List<GNM_WareHouse>();
                string cultureValue = SelectConsigneeObj.GeneralCulture.ToString();
                string resourceValue = _coreServiceResourceHelper.GetResourceString(cultureValue, "select");
                string JsonWareHouse = "-1:--" + resourceValue + "--;";
                string JsonWareHouse2 = "-1:--" + resourceValue + "--;";
                //string JsonWareHouse = "-1:--" + GetGlobalResourceObject(SelectConsigneeObj.GeneralCulture.ToString(), "select").ToString() + "--;";

                //string JsonWareHouse2 = "-1:--" + GetGlobalResourceObject(SelectConsigneeObj.GeneralCulture.ToString(), "select").ToString() + "--;";

                if (SelectConsigneeObj.LanguageID == Convert.ToInt32(SelectConsigneeObj.GeneralLanguageID))
                {
                    //IEConsigneeMasterArray = (from a in IEConsigneeList
                    //                          join b in WareHouseClient.GNM_WareHouse on a.WareHouse_ID equals b.WareHouse_ID into wrhus
                    //                          from conwrhus in wrhus.DefaultIfEmpty(new GNM_WareHouse { WareHouse_ID = 0, WareHouseName = "" })
                    //                          select new ConsigneeMaster()
                    //                          {
                    //                              Consignee_ID = a.Consignee_ID,
                    //                              Consignee_Location = a.ConsigneeLocation,
                    //                              Consignee_Address = a.ConsigneeAddress,
                    //                              WareHouse_ID = a.WareHouse_ID,
                    //                              WareHouse = conwrhus.WareHouseName,
                    //                              Consignee_IsDefault = (a.IsDefault == true ? YesE : NoE),
                    //                              Consignee_IsActive = (a.IsActive == true ? YesE : NoE),
                    //                              IsDefault = a.IsDefault

                    //                          });
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "USP_GetWareHouseDetails";



                        try
                        {
                            using (SqlCommand command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@CompanyID", Company_ID);
                                command.Parameters.AddWithValue("@BranchID", SelectConsigneeObj.Branch_ID);
                                command.Parameters.AddWithValue("@LanguageID", SelectConsigneeObj.LanguageID);
                                command.Parameters.AddWithValue("@GenLanguageID", Convert.ToInt32(SelectConsigneeObj.GeneralLanguageID));

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        GNM_WareHouse wareHouse = new GNM_WareHouse
                                        {
                                            WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            WareHouseName = reader.IsDBNull(reader.GetOrdinal("WareHouseName")) ? null : reader.GetString(reader.GetOrdinal("WareHouseName"))
                                        };
                                        WareHouseList.Add(wareHouse);
                                    }
                                    reader.NextResult();
                                    while (reader.Read())
                                    {
                                        GNM_WareHouse warehouse = new GNM_WareHouse
                                        {
                                            WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            WareHouseName = reader.IsDBNull(reader.GetOrdinal("WareHouseName")) ? null : reader.GetString(reader.GetOrdinal("WareHouseName"))
                                        };
                                        WareHouseList2.Add(warehouse);
                                    }

                                }
                            }
                        }
                        catch
                        {

                        }
                    }

                    for (int i = 0; i < WareHouseList.Count(); i++)
                    {
                        JsonWareHouse = JsonWareHouse + WareHouseList.ElementAt(i).WareHouse_ID + ":" + WareHouseList.ElementAt(i).WareHouseName + ";";
                    }

                    for (int i = 0; i < WareHouseList2.Count(); i++)
                    {
                        JsonWareHouse2 = JsonWareHouse2 + WareHouseList2.ElementAt(i).WareHouse_ID + ":" + WareHouseList2.ElementAt(i).WareHouseName + ";";
                    }

                }
                else
                {
                    //IEConsigneeMasterArray = from a in IEConsigneeList
                    //                         join b in IEConsigneeLocaleList on a.Consignee_ID equals b.Consignee_ID
                    //                         join c in WareHouseClient.GNM_WareHouseLocale on a.WareHouse_ID equals c.WareHouse_ID into wrhus
                    //                         from conwrhus in wrhus.DefaultIfEmpty(new GNM_WareHouseLocale { WareHouse_ID = 0, WareHouseName = "" })
                    //                         select new ConsigneeMaster()
                    //                         {
                    //                             Consignee_ID = b.Consignee_ID,
                    //                             Consignee_Location = b.ConsigneeLocation,
                    //                             Consignee_Address = b.ConsigneeAddress,
                    //                             WareHouse_ID = a.WareHouse_ID,
                    //                             WareHouse = conwrhus.WareHouseName,
                    //                             Consignee_IsDefault = (a.IsDefault == true ? YesL : NoL),
                    //                             Consignee_IsActive = (a.IsActive == true ? YesL : NoL),
                    //                             IsDefault = a.IsDefault
                    //                         };

                    //var WareHouseLocaleArray = (from a in WareHouseList
                    //                            join b in WareHouseClient.GNM_WareHouseLocale on a.WareHouse_ID equals b.WareHouse_ID
                    //                            where b.Language_ID == LanguageID
                    //                            select new
                    //                            {
                    //                                b.WareHouse_ID,
                    //                                b.WareHouseName
                    //                            });
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "USP_GetWareHouseDetails";



                        try
                        {
                            using (SqlCommand command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@CompanyID", Company_ID);
                                command.Parameters.AddWithValue("@BranchID", SelectConsigneeObj.Branch_ID);
                                command.Parameters.AddWithValue("@LanguageID", SelectConsigneeObj.LanguageID);
                                command.Parameters.AddWithValue("@GenLanguageID", Convert.ToInt32(SelectConsigneeObj.GeneralLanguageID));

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        GNM_WareHouse wareHouse = new GNM_WareHouse
                                        {
                                            WareHouse_ID = reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            WareHouseName = reader.IsDBNull(reader.GetOrdinal("WareHouseName")) ? null : reader.GetString(reader.GetOrdinal("WareHouseName"))
                                        };
                                        WareHouseList.Add(wareHouse);
                                    }

                                    WareHouseLocaleArray = WareHouseList;
                                }
                            }
                        }
                        catch
                        {

                        }
                    }

                    for (int i = 0; i < WareHouseLocaleArray.ToList().Count(); i++)
                    {
                        JsonWareHouse = JsonWareHouse + WareHouseLocaleArray.ElementAt(i).WareHouse_ID + ":" + WareHouseLocaleArray.ElementAt(i).WareHouseName + ";";
                    }

                }

                JsonWareHouse = JsonWareHouse.TrimEnd(new char[] { ';' });
                JsonWareHouse2 = JsonWareHouse2.TrimEnd(new char[] { ';' });
                var GetAllconsigneeObj = new GetAllconsigneeList
                {
                    Branch_ID = SelectConsigneeObj.Branch_ID,
                    LanguageID = SelectConsigneeObj.LanguageID,
                    GeneralCulture = SelectConsigneeObj.GeneralCulture,
                    UserCulture = SelectConsigneeObj.UserCulture,
                    GeneralLanguageID = SelectConsigneeObj.GeneralLanguageID

                };

                IQConsigneeMaster = await GetAllconsignee(GetAllconsigneeObj, connString, LogException);

                if (_search)
                {
                    string decodedValue = Uri.UnescapeDataString(filters);
                    string decryptedValue = await DecryptStringAsync(decodedValue);
                    Filters filterObj = JObject.Parse(decryptedValue).ToObject<Filters>();
                    if (filterObj.rules.Count > 0)
                    {
                        var consigneeMasterArray = IQConsigneeMaster.ToArray();
                        var filteredArray = await FilterSearchAsync(consigneeMasterArray, filterObj);
                        IQConsigneeMaster = filteredArray.AsQueryable();
                    }
                }

                if (advnce)
                {
                    string decodedAdvanceFilter = Uri.UnescapeDataString(advanceFilter);
                    string decryptedAdvanceFilter = await DecryptStringAsync(decodedAdvanceFilter);
                    AdvanceFilter advnfilterObj = JObject.Parse(decryptedAdvanceFilter).ToObject<AdvanceFilter>();
                    var consigneeMasterArray = IQConsigneeMaster.ToArray();
                    var filteredArray = await AdvanceSearchAsync(consigneeMasterArray, advnfilterObj);
                    IQConsigneeMaster = filteredArray.AsQueryable();
                }

                var consigneeMasterArrayForSort = IQConsigneeMaster.ToArray();
                var sortedArray = await OrderByFieldAsync(consigneeMasterArrayForSort, sidx, sord);
                IQConsigneeMaster = sortedArray.AsQueryable();

                //if (LanguageID == Convert.ToInt32(Session["GeneralLanguageID"]))
                //{
                //    Session["IQConsigneeMaster"] = IQConsigneeMaster.AsEnumerable();
                //}

                Count = IQConsigneeMaster.Count();
                Total = rows > 0 ? Convert.ToInt32(Math.Ceiling(Convert.ToDouble(Count) / Convert.ToDouble(rows))) : 0;

                if (Count < (rows * page) && Count != 0)
                {
                    page = (Count / rows) + ((Count % rows) == 0 ? 0 : 1);
                }

                string viewResourceString = _coreServiceResourceHelper.GetResourceString(SelectConsigneeObj.UserCulture.ToString(), "view");
                var paginatedData = await PaginateAsync(IQConsigneeMaster.ToArray(), page, rows);

                jsonData = new
                {
                    total = Total,
                    page = page,
                    rows = (from a in paginatedData
                            select new
                            {
                                ID = a.Consignee_ID,
                                edit = "<a title=" + viewResourceString + " href='#' style='font-size: 13px;' key='" + a.Consignee_ID + "' mode='Read' " + "  class='ConsigneeEdit'  editmode='false' style='cursor:pointer'><i class='fa-solid fa-arrow-up-right-from-square ClsViewIcon'></i></a>",
                                //edit = "<img id='" + a.Consignee_ID + "' src='" + AppPath + "/Content/edit.gif' key='" + a.Consignee_ID + "' class='ConsigneeEdit' editmode='false'/>",
                                delete = "<input type='checkbox' key='" + a.Consignee_ID + "' defaultchecked=''  id='chk" + a.Consignee_ID + "'  " + ((a.IsDefault == true) ? "class='CannotDeleteConsignee'" : "class='ConsigneeDelete'") + "/>",
                                Consignee_Location = a.Consignee_Location,
                                Consignee_Address = a.Consignee_Address,
                                WareHouse = a.WareHouse,
                                Consignee_IsDefault = a.Consignee_IsDefault,
                                Consignee_IsActive = a.Consignee_IsActive,
                                Locale = "<span class='input-sm font-focus ConsigneeLocale' key='" + a.Consignee_ID + "'><i class='fa fa-globe fa-2x' data-toggle='tooltip'></i></span>",
                                //Locale = "<img key='" + a.Consignee_ID + "' src='" + AppPath + "/Content/local.png' class='ConsigneeLocale' alt='Localize' width='20' height='20'  title='Localize'/>",
                                View = "<img id='" + a.Consignee_ID + "' src='" + AppPath + "/Content/plus.gif' key='" + a.Consignee_ID + "' class='ViewConsigneeLocale' Consignee='" + a.Consignee_Location + "'/>",
                            }).ToList(),
                    records = Count,
                    JsonWareHouse,
                    JsonWareHouse2,
                    filter = filters,
                    advanceFilter = advanceFilter,
                    SelectConsigneeObj.Branch_ID,
                    SelectConsigneeObj.LanguageID
                };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(jsonData);
        }
        #endregion
        #region GetAllconsignee Vinay N 19/8/24
        /// <summary>
        /// GetAllconsignee
        /// </summary>
        /// <param name="GetAllconsigneeObj"></param>
        /// <param name="connstring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public async Task<IQueryable<ConsigneeMaster>> GetAllconsignee(GetAllconsigneeList GetAllconsigneeObj, string connstring, int LogException)
        {
            IQueryable<ConsigneeMaster> IQConsigneeMaster = null;
            List<ConsigneeMaster> IEConsigneeMasterArray = new List<ConsigneeMaster>();
            try
            {


                // Get resources for the general culture
                string YesE =  _coreServiceResourceHelper.GetResourceString(GetAllconsigneeObj.GeneralCulture.ToString(), "yes");
                string NoE =  _coreServiceResourceHelper.GetResourceString(GetAllconsigneeObj.GeneralCulture.ToString(), "no");

                // Get resources for the user culture
                string YesL =  _coreServiceResourceHelper.GetResourceString(GetAllconsigneeObj.UserCulture.ToString(), "yes");
                string NoL =  _coreServiceResourceHelper.GetResourceString(GetAllconsigneeObj.UserCulture.ToString(), "no");


                if (GetAllconsigneeObj.LanguageID == Convert.ToInt32(GetAllconsigneeObj.GeneralLanguageID))
                {


                    using (SqlConnection conn = new SqlConnection(connstring))
                    {
                        string query = "USP_GetAllconsignee";



                        try
                        {
                            using (SqlCommand command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@YesE", YesE);
                                command.Parameters.AddWithValue("@NoE", NoE);
                                command.Parameters.AddWithValue("@YesL", YesL);
                                command.Parameters.AddWithValue("@NoL", NoL);

                                command.Parameters.AddWithValue("@BranchID", GetAllconsigneeObj.Branch_ID);
                                command.Parameters.AddWithValue("@LanguageID", GetAllconsigneeObj.LanguageID);
                                command.Parameters.AddWithValue("@GenLanguageID", Convert.ToInt32(GetAllconsigneeObj.GeneralLanguageID));

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var consigneeMaster = new ConsigneeMaster
                                        {
                                            Consignee_ID = reader.GetInt32(reader.GetOrdinal("Consignee_ID")),
                                            Consignee_Location = reader.IsDBNull(reader.GetOrdinal("Consignee_Location")) ? null : reader.GetString(reader.GetOrdinal("Consignee_Location")),
                                            Consignee_Address = reader.IsDBNull(reader.GetOrdinal("Consignee_Address")) ? null : reader.GetString(reader.GetOrdinal("Consignee_Address")),
                                            WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            WareHouse = reader.IsDBNull(reader.GetOrdinal("WareHouse")) ? null : reader.GetString(reader.GetOrdinal("WareHouse")),
                                            Consignee_IsDefault = reader.IsDBNull(reader.GetOrdinal("Consignee_IsDefault")) ? null : reader.GetString(reader.GetOrdinal("Consignee_IsDefault")),
                                            Consignee_IsActive = reader.IsDBNull(reader.GetOrdinal("Consignee_IsActive")) ? null : reader.GetString(reader.GetOrdinal("Consignee_IsActive")),
                                            IsDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault"))
                                        };

                                        IEConsigneeMasterArray.Add(consigneeMaster);
                                    }


                                }
                            }
                        }
                        catch
                        {

                        }
                    }

                }
                else
                {


                    using (SqlConnection conn = new SqlConnection(connstring))
                    {
                        string query = "USP_GetAllconsignee";



                        try
                        {
                            using (SqlCommand command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@YesE", YesE);
                                command.Parameters.AddWithValue("@NoE", NoE);
                                command.Parameters.AddWithValue("@YesL", YesL);
                                command.Parameters.AddWithValue("@NoL", NoL);

                                command.Parameters.AddWithValue("@BranchID", GetAllconsigneeObj.Branch_ID);
                                command.Parameters.AddWithValue("@LanguageID", GetAllconsigneeObj.LanguageID);
                                command.Parameters.AddWithValue("@GenLanguageID", Convert.ToInt32(GetAllconsigneeObj.GeneralLanguageID));

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                using (SqlDataReader reader = command.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        var consigneeMaster = new ConsigneeMaster
                                        {
                                            Consignee_ID = reader.GetInt32(reader.GetOrdinal("Consignee_ID")),
                                            Consignee_Location = reader.IsDBNull(reader.GetOrdinal("Consignee_Location")) ? null : reader.GetString(reader.GetOrdinal("Consignee_Location")),
                                            Consignee_Address = reader.IsDBNull(reader.GetOrdinal("Consignee_Address")) ? null : reader.GetString(reader.GetOrdinal("Consignee_Address")),
                                            WareHouse_ID = reader.IsDBNull(reader.GetOrdinal("WareHouse_ID")) ? 0 : reader.GetInt32(reader.GetOrdinal("WareHouse_ID")),
                                            WareHouse = reader.IsDBNull(reader.GetOrdinal("WareHouse")) ? null : reader.GetString(reader.GetOrdinal("WareHouse")),
                                            Consignee_IsDefault = reader.IsDBNull(reader.GetOrdinal("Consignee_IsDefault")) ? null : reader.GetString(reader.GetOrdinal("Consignee_IsDefault")),
                                            Consignee_IsActive = reader.IsDBNull(reader.GetOrdinal("Consignee_IsActive")) ? null : reader.GetString(reader.GetOrdinal("Consignee_IsActive")),
                                            IsDefault = reader.GetBoolean(reader.GetOrdinal("IsDefault"))
                                        };

                                        IEConsigneeMasterArray.Add(consigneeMaster);
                                    }


                                }
                            }
                        }
                        catch
                        {

                        }
                    }


                }

                IQConsigneeMaster = IEConsigneeMasterArray.AsQueryable<ConsigneeMaster>();
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return IQConsigneeMaster;
        }
        #endregion

        #region Save Vinay N 19/8/24
        /// <summary>
        /// Save
        /// </summary>
        /// <param name="SaveObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> Save(SaveList SaveObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {


                var jObj = JObject.Parse(SaveObj.data);
                int Count = jObj["rows"].Count();
                int BranchID = Convert.ToInt32(SaveObj.Branch);

                List<GNM_Consignee> IConsigneeList = null;
                bool tableUpdated = false;
                for (int i = 0; i < Count; i++)
                {
                    GNM_Consignee SRow = jObj["rows"].ElementAt(i).ToObject<GNM_Consignee>();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "USP_SaveConsignee";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Consignee_ID", SRow.Consignee_ID);
                                command.Parameters.AddWithValue("@tableUpdated", tableUpdated);
                                command.Parameters.AddWithValue("@BranchID", SRow.Branch_ID);
                                command.Parameters.AddWithValue("@CompanyID", SRow.Company_ID);
                                command.Parameters.AddWithValue("@ConsigneeLocation", SRow.ConsigneeLocation);
                                command.Parameters.AddWithValue("@ConsigneeAddress", SRow.ConsigneeAddress);
                                command.Parameters.AddWithValue("@WarehouseID", SRow.WareHouse_ID);
                                command.Parameters.AddWithValue("@IsActive", SRow.IsActive);
                                command.Parameters.AddWithValue("@IsDefault", SRow.IsDefault);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                int updated = command.ExecuteNonQuery();
                                if (updated > 0)
                                {
                                    tableUpdated = true;
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                            }
                            Msg = string.Empty;
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }


                    if (SRow.Consignee_ID != 0)
                    {





                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID.ToString()), BranchID, SaveObj.User_ID, Common.GetObjectID("CoreConsignee"), SRow.Consignee_ID, 0, 0, "Updated Consignee ", false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));

                    }
                    else
                    {
                        // gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreConsignee")), SRow.Consignee_ID, 0, 0, "Insert", false);
                        //gbl.InsertGPSDetails(Convert.ToInt32(SaveObj.Company_ID.ToString()), BranchID, SaveObj.User_ID, Common.GetObjectID("CoreConsignee"), SRow.Consignee_ID, 0, 0, "Inserted Consignee ", false, Convert.ToInt32(SaveObj.MenuID), Convert.ToDateTime(SaveObj.LoggedINDateTime));
                    }

                    if (SRow.IsDefault) { tableUpdated = true; }
                }
                if (tableUpdated)
                {

                    Msg = "Saved";
                }
                else { Msg = "UnSaved"; }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");

                }
                Msg = string.Empty;
            }
            return new JsonResult(Msg);

        }
        #endregion

        #region ::: CheckConsignee  Vinay N 20/8/24
        /// <summary>
        /// CheckConsignee
        /// </summary>
        /// <param name="CheckConsigneeObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> CheckConsignee(CheckConsigneeList CheckConsigneeObj, string connString, int LogException)
        {
            int Count = 0;
            var Msg = string.Empty;
            try
            {


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_GetConsigneeRows";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@BranchID", CheckConsigneeObj.BranchID);
                            command.Parameters.AddWithValue("@ConsigneeLocation", CheckConsigneeObj.ConsigneeLocation);
                            command.Parameters.AddWithValue("@ConsigneeID", CheckConsigneeObj.ConsigneeID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            int count = (int)command.ExecuteScalar();
                            if (count > 0)
                            {
                                Count = 1;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                        }
                        Msg = string.Empty;
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }

            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region ::: CheckConsigneeAddress  Vinay N 20/8/24
        /// <summary>
        /// CheckConsigneeAddress
        /// </summary>
        /// <param name="CheckConsigneeAddressObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> CheckConsigneeAddress(CheckConsigneeAddressList CheckConsigneeAddressObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {


                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_GetConsigneeAddressRows";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@BranchID", CheckConsigneeAddressObj.BranchID);
                            command.Parameters.AddWithValue("@ConsigneeAddress", CheckConsigneeAddressObj.ConsigneeAddress);
                            command.Parameters.AddWithValue("@ConsigneeID", CheckConsigneeAddressObj.ConsigneeID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            int count = (int)command.ExecuteScalar();
                            if (count > 0)
                            {
                                Count = 1;
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                        }
                        var Msg = string.Empty;
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region SelectParticularConsignee Vinay N 20/8/24
        /// <summary>
        /// SelectParticularConsignee
        /// </summary>
        /// <param name="SelectParticularConsigneeObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> SelectParticularConsignee(SelectParticularConsigneeList SelectParticularConsigneeObj, string connString, int LogException)
        {
            var x = default(dynamic);
            using (SqlConnection conn = new SqlConnection(connString))
            {
                string query = "UP_Select_AM_ERP_SelectParticularConsigneeDetails";

                SqlCommand command = null;

                try
                {
                    using (command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@ConsigneeID", SelectParticularConsigneeObj.ConsigneeID);
                        command.Parameters.AddWithValue("@Language_ID", SelectParticularConsigneeObj.Language_ID);

                        if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                        {
                            conn.Open();
                        }
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                x = new
                                {
                                    Consignee_ID = reader.GetInt32(reader.GetOrdinal("Consignee_ID")),
                                    Consignee_Location = reader.IsDBNull(reader.GetOrdinal("Consignee_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("Consignee_Location")),
                                    Consignee_Address = reader.IsDBNull(reader.GetOrdinal("Consignee_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("Consignee_Address")),
                                    WareHouse = reader.IsDBNull(reader.GetOrdinal("WareHouse")) ? string.Empty : reader.GetString(reader.GetOrdinal("WareHouse")),
                                    Consignee_IsDefault = reader.GetBoolean(reader.GetOrdinal("Consignee_IsDefault")),
                                    Consignee_IsActive = reader.GetBoolean(reader.GetOrdinal("Consignee_IsActive")),
                                    ConsigneeLocale_ID = reader.IsDBNull(reader.GetOrdinal("ConsigneeLocale_ID")) ? string.Empty : reader.GetInt32(reader.GetOrdinal("ConsigneeLocale_ID")).ToString(),

                                    ConsigneeLocale_Location = reader.IsDBNull(reader.GetOrdinal("ConsigneeLocale_Location")) ? string.Empty : reader.GetString(reader.GetOrdinal("ConsigneeLocale_Location")),
                                    ConsigneeLocale_Address = reader.IsDBNull(reader.GetOrdinal("ConsigneeLocale_Address")) ? string.Empty : reader.GetString(reader.GetOrdinal("ConsigneeLocale_Address")),
                                    WareHouseLocale = reader.IsDBNull(reader.GetOrdinal("WareHouseLocale")) ? string.Empty : reader.GetString(reader.GetOrdinal("WareHouseLocale"))
                                };
                            }



                        }
                    }
                }
                catch (Exception ex)
                {
                    if (LogException == 1)
                    {
                        await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                    }

                }
                finally
                {
                    command.Dispose();
                    conn.Close();
                    conn.Dispose();
                    SqlConnection.ClearAllPools();
                }
            }
            return new JsonResult(x);
        }
        #endregion
        #region Delete vinay N 20/8/24
        /// <summary>
        /// Delete
        /// </summary>
        /// <param name="DeleteObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>

        public async Task<IActionResult> Delete(DeleteList DeleteObj, string connString, int LogException)
        {
            var Msg = string.Empty;
            try
            {

                var jObj = JObject.Parse(DeleteObj.key);
                int Count = jObj["rows"].Count();
                int BranchID = Convert.ToInt32(DeleteObj.Branch);

                GNM_Consignee deleteRow = null;
                GNM_ConsigneeLocale localeRow = null;
                int ID = 0;
                int status = 0;
                for (int i = 0; i < Count; i++)
                {
                    var jTR = new JTokenReader(jObj["rows"].ElementAt(i).ToObject<JObject>()["id"]);
                    jTR.Read();
                    ID = Convert.ToInt32(jTR.Value);
                    //deleteRow = ConsigneeClient.GNM_Consignee.Where(a => a.Consignee_ID == ID).FirstOrDefault();
                    //localeRow = ConsigneeClient.GNM_ConsigneeLocale.Where(a => a.Consignee_ID == ID).FirstOrDefault();
                    //ConsigneeClient.GNM_Consignee.Remove(deleteRow);
                    //if (localeRow != null)
                    //{
                    //    ConsigneeClient.GNM_ConsigneeLocale.Remove(localeRow);
                    //}
                    //ConsigneeClient.SaveChanges();
                    using (SqlConnection conn = new SqlConnection(connString))
                    {
                        string query = "USP_DeleteConsignee";

                        SqlCommand command = null;

                        try
                        {
                            using (command = new SqlCommand(query, conn))
                            {
                                command.CommandType = CommandType.StoredProcedure;
                                command.Parameters.AddWithValue("@Consignee_ID", ID);

                                if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                                {
                                    conn.Open();
                                }
                                int updated = command.ExecuteNonQuery();
                                if (updated > 0)
                                {
                                    status = 1;
                                }


                            }
                        }
                        catch (Exception ex)
                        {
                            if (LogException == 1)
                            {
                                await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                            }
                            Msg = string.Empty;
                        }
                        finally
                        {
                            command.Dispose();
                            conn.Close();
                            conn.Dispose();
                            SqlConnection.ClearAllPools();
                        }
                    }
                    if (status == 1)
                    {
                        //gbl.InsertGPSDetails(Convert.ToInt32(DeleteObj.Company_ID.ToString()), BranchID, DeleteObj.User_ID, Common.GetObjectID("CoreConsignee"), ID, 0, 0, "Deleted Consignee ", false, Convert.ToInt32(DeleteObj.MenuID), Convert.ToDateTime(DeleteObj.LoggedINDateTime));
                    }
                }

                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreConsignee")), ID, 0, 0, "Delete", false);
                Msg +=  _coreServiceResourceHelper.GetResourceString(DeleteObj.GeneralCulture.ToString(), "deletedsuccessfully");
            }
            catch (Exception ex)
            {
                if (ex.InnerException?.InnerException?.Message?.Contains("The DELETE statement conflicted with the REFERENCE constraint") == true)
                {
                    Msg +=  _coreServiceResourceHelper.GetResourceString(DeleteObj.GeneralCulture.ToString(), "Dependencyfoundcannotdeletetherecords");
                }
            }
            return new JsonResult(Msg);
        }
        #endregion
        #region UpdateLocale Vinay 20/08/24
        /// <summary>
        /// UpdateLocale
        /// </summary>
        /// <param name="UpdateLocaleObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<ActionResult> UpdateLocale(UpdateLocaleList UpdateLocaleObj, string connString, int LogException)
        {
            int ConsigneeLocale_ID = 0;
            var x = default(dynamic);
            try
            {

                GNM_ConsigneeLocale SLRow = null;
                GNM_ConsigneeLocale AddRow = null;
                var jObj = JObject.Parse(UpdateLocaleObj.data);

                SLRow = jObj.ToObject<GNM_ConsigneeLocale>();
                //if (SLRow.ConsigneeLocale_ID != 0)
                //{
                //    GNM_ConsigneeLocale UpdateConsigneeLocale = ConsigneeClient.GNM_ConsigneeLocale.Where(a => a.ConsigneeLocale_ID == SLRow.ConsigneeLocale_ID).FirstOrDefault();
                //    UpdateConsigneeLocale.ConsigneeLocation = Common.DecryptString(SLRow.ConsigneeLocation);
                //    UpdateConsigneeLocale.ConsigneeAddress = Common.DecryptString(SLRow.ConsigneeAddress);
                //    ConsigneeLocale_ID = SLRow.ConsigneeLocale_ID;
                //}
                //else
                //{
                //    SLRow.ConsigneeLocation = Common.DecryptString(SLRow.ConsigneeLocation);
                //    SLRow.ConsigneeAddress = Common.DecryptString(SLRow.ConsigneeAddress);
                //    AddRow = ConsigneeClient.GNM_ConsigneeLocale.Add(SLRow);

                //}
                //ConsigneeClient.SaveChanges();
                //gbl.InsertGPSDetails(Convert.ToInt32(Session["Company_ID"]), Convert.ToInt32(Session["Branch"]), Convert.ToInt32(Session["User_ID"]), Convert.ToInt32(Common.GetObjectID("CoreConsignee")), SLRow.Consignee_ID, 0, 0, "Update", false, Convert.ToInt32(Session["MenuID"]));
                //ConsigneeClient = new ConsigneeEntities();
                //if (AddRow != null)
                //{
                //    ConsigneeLocale_ID = AddRow.ConsigneeLocale_ID;
                //}

                //ConsigneeClient = new ConsigneeEntities();
                //WareHouseClient = new WareHouseEntities();
                //GNM_Consignee ConsigneeList = ConsigneeClient.GNM_Consignee.Where(a => a.Consignee_ID == SLRow.Consignee_ID).FirstOrDefault();
                List<GNM_WareHouseLocale> WareHouseLocaleList = new List<GNM_WareHouseLocale>();
                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_UpdateConsigneeLocale";

                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@ConsigneeLocale_ID", SLRow.ConsigneeLocale_ID);
                            command.Parameters.AddWithValue("@Consignee_ID", SLRow.Consignee_ID);
                            command.Parameters.AddWithValue("@ConsigneeLocation", SLRow.ConsigneeLocation);
                            command.Parameters.AddWithValue("@ConsigneeAddress", SLRow.ConsigneeAddress);
                            command.Parameters.AddWithValue("@Language_ID", SLRow.Language_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }
                            using (SqlDataReader reader = command.ExecuteReader())
                            {
                                // Reading the ConsigneeLocale_ID
                                if (reader.Read())
                                {
                                    ConsigneeLocale_ID = reader.GetInt32(reader.GetOrdinal("ConsigneeLocale_ID"));
                                }


                                reader.NextResult();


                                while (reader.Read())
                                {
                                    GNM_WareHouseLocale wareHouse = new GNM_WareHouseLocale
                                    {
                                        WareHouseName = reader.IsDBNull(reader.GetOrdinal("WareHouseName")) ? null : reader.GetString(reader.GetOrdinal("WareHouseName"))
                                    };
                                    WareHouseLocaleList.Add(wareHouse);
                                }


                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                        }
                        var Msg = string.Empty;
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
                //gbl.InsertGPSDetails(Convert.ToInt32(UpdateLocaleObj.Company_ID), Convert.ToInt32(UpdateLocaleObj.Branch), Convert.ToInt32(UpdateLocaleObj.User_ID), Convert.ToInt32(Common.GetObjectID("CoreConsignee")), SLRow.Consignee_ID, 0, 0, "Update", false, Convert.ToInt32(UpdateLocaleObj.MenuID));
                x = new
                {
                    ConsigneeLocale_ID = ConsigneeLocale_ID,
                    WareHouseLocale = (WareHouseLocaleList != null && WareHouseLocaleList.Count > 0) ? WareHouseLocaleList[0].WareHouseName : ""
                };


            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(x);
        }
        #endregion

        #region :::vinay n/20/08/24
        /// <summary>
        /// CheckConsigneeLocale
        /// </summary>
        /// <param name="CheckConsigneeLocaleObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> CheckConsigneeLocale(CheckConsigneeLocaleList CheckConsigneeLocaleObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {

                int Language_ID = Convert.ToInt32(CheckConsigneeLocaleObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_GetConsigneeLocaleRows";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@BranchID", CheckConsigneeLocaleObj.BranchID);
                        command.Parameters.AddWithValue("@ConsigneeLocation", CheckConsigneeLocaleObj.ConsigneeLocation);
                        command.Parameters.AddWithValue("@ConsigneeLocaleID", CheckConsigneeLocaleObj.ConsigneeLocaleID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        conn.Open();
                        int count = (int)command.ExecuteScalar();
                        if (count > 0)
                        {
                            Count = 1;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(Count);
        }
        #endregion


        #region CheckConsigneeAddressLocale Vinay n 20/08/24
        /// <summary>
        /// CheckConsigneeAddressLocale
        /// </summary>
        /// <param name="CheckConsigneeAddressLocaleObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> CheckConsigneeAddressLocale(CheckConsigneeAddressLocaleList CheckConsigneeAddressLocaleObj, string connString, int LogException)
        {
            int Count = 0;
            try
            {

                int Language_ID = Convert.ToInt32(CheckConsigneeAddressLocaleObj.UserLanguageID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_GetConsigneeAddressLocaleRows";

                    using (SqlCommand command = new SqlCommand(query, conn))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        command.Parameters.AddWithValue("@BranchID", CheckConsigneeAddressLocaleObj.BranchID);
                        command.Parameters.AddWithValue("@ConsigneeAddress", CheckConsigneeAddressLocaleObj.ConsigneeAddress);
                        command.Parameters.AddWithValue("@ConsigneeLocaleID", CheckConsigneeAddressLocaleObj.ConsigneeLocaleID);
                        command.Parameters.AddWithValue("@LanguageID", Language_ID);

                        conn.Open();
                        int count = (int)command.ExecuteScalar();
                        if (count > 0)
                        {
                            Count = 1;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return new JsonResult(Count);
        }
        #endregion
        #region CheckWareHouse vinay n 20/08/24
        /// <summary>
        /// CheckWareHouse
        /// </summary>
        /// <param name="CheckWareHouseObj"></param>
        /// <param name="connString"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public async Task<IActionResult> CheckWareHouse(CheckWareHouseList CheckWareHouseObj, string connString, int LogException)
        {
            int status = 0;
            try
            {

                int Company_ID = Convert.ToInt32(CheckWareHouseObj.Company_ID);

                using (SqlConnection conn = new SqlConnection(connString))
                {
                    string query = "USP_GetConsigneeWareHouseRow";
                    SqlCommand command = null;

                    try
                    {
                        using (command = new SqlCommand(query, conn))
                        {
                            command.CommandType = CommandType.StoredProcedure;

                            command.Parameters.AddWithValue("@WareHouse_ID", CheckWareHouseObj.WareHouse_ID);
                            command.Parameters.AddWithValue("@BranchID", CheckWareHouseObj.BranchID);
                            command.Parameters.AddWithValue("@CompanyID", Company_ID);
                            command.Parameters.AddWithValue("@ConsigneeID", CheckWareHouseObj.Consignee_ID);

                            if (conn.State == ConnectionState.Closed || conn.State == ConnectionState.Broken)
                            {
                                conn.Open();
                            }

                            int count = (int)command.ExecuteScalar();
                            if (count > 0)
                            {
                                status = 1;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        if (LogException == 1)
                        {
                            await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                        }
                        return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
                    }
                    finally
                    {
                        command.Dispose();
                        conn.Close();
                        conn.Dispose();
                        SqlConnection.ClearAllPools();
                    }
                }
            }
            catch (WebException wex)
            {
                await LogToUtilityServiceAsync(wex.HResult, wex.Status.ToString(), wex.TargetSite?.ToString() ?? "", wex.StackTrace ?? "");
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
                return new JsonResult(new { Error = "An error occurred" }) { StatusCode = 500 };
            }
            //return status;
            return new JsonResult(status);
        }
        #endregion

        #region ::: Export :::
        /// <summary>
        /// To Export
        /// </summary>
        public async Task<object> Export(GetAllconsigneeList ExportObj, string connString, int LogException, string filter, string advanceFilter, string sidx, string sord)
        {
            int Count = 0;
            try
            {
                IQueryable<ConsigneeMaster> IQConsigneeMaster = null;
                DataTable DtData = new DataTable();

                int Branch_ID = Convert.ToInt32(ExportObj.Branch_ID.ToString());
                int LanguageID = Convert.ToInt32(ExportObj.LanguageID.ToString());

                IQConsigneeMaster = await GetAllconsignee(ExportObj, connString, LogException);

                if (filter != "null")
                {
                    string decodedFilter = Uri.UnescapeDataString(filter);
                    string decryptedFilter = await DecryptStringAsync(decodedFilter);
                    Filters filters = JObject.Parse(decryptedFilter).ToObject<Filters>();
                    if (filters?.rules?.Count > 0)
                    {
                        var consigneeMasterArray = IQConsigneeMaster.ToArray();
                        var filteredArray = await FilterSearchAsync(consigneeMasterArray, filters);
                        IQConsigneeMaster = filteredArray.AsQueryable();
                    }
                }

                if (advanceFilter != "null")
                {
                    string decodedAdvanceFilter = Uri.UnescapeDataString(advanceFilter);
                    string decryptedAdvanceFilter = await DecryptStringAsync(decodedAdvanceFilter);
                    AdvanceFilter advnfilter = JObject.Parse(decryptedAdvanceFilter).ToObject<AdvanceFilter>();
                    if (advnfilter != null)
                    {
                        var consigneeMasterArray = IQConsigneeMaster.ToArray();
                        var filteredArray = await AdvanceSearchAsync(consigneeMasterArray, advnfilter);
                        IQConsigneeMaster = filteredArray.AsQueryable();
                    }
                }

                var orderedArray = IQConsigneeMaster.ToArray();
                var sortedArray = await OrderByFieldAsync(orderedArray, sidx.ToString(), sord.ToString());
                IQConsigneeMaster = sortedArray.AsQueryable();

                // Create DataTable columns with localized headers
                DtData.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "ConsigneeLocation"));
                DtData.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "ConsigneeAddress"));
                DtData.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "Warehouse"));
                DtData.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "IsDefault"));
                DtData.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "Active"));

                Count = IQConsigneeMaster.AsEnumerable().Count();
                if (Count > 0)
                {
                    for (int i = 0; i < Count; i++)
                    {
                        DtData.Rows.Add(
                            IQConsigneeMaster.ElementAt(i).Consignee_Location,
                            IQConsigneeMaster.ElementAt(i).Consignee_Address,
                            IQConsigneeMaster.ElementAt(i).WareHouse,
                            IQConsigneeMaster.ElementAt(i).Consignee_IsDefault,
                            IQConsigneeMaster.ElementAt(i).Consignee_IsActive
                        );
                    }

                    DataTable DtCriteria = new DataTable();
                    DataTable DtAlignment = new DataTable();

                    DtAlignment.Columns.Add("Consignee");
                    DtAlignment.Columns.Add("ConsigneeAddress");
                    DtAlignment.Columns.Add("Warehouse");
                    DtAlignment.Columns.Add("IsDefault");
                    DtAlignment.Columns.Add("Active");
                    DtAlignment.Rows.Add(0, 0, 0, 1, 1);

                    DtCriteria.Columns.Add( _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "Branch"));
                    string decryptedBranchName = await DecryptStringAsync(ExportObj.BranchName);
                    DtCriteria.Rows.Add(decryptedBranchName);

                    var reportExportList = new ReportExportList
                    {
                        Company_ID = ExportObj.Company_ID,
                        Branch = ExportObj.Branch_ID.ToString(),
                        GeneralLanguageID = LanguageID,
                        UserLanguageID = ExportObj.UserLanguageID,
                        Options = DtCriteria,
                        dt = DtData,
                        Alignment = DtAlignment,
                        FileName = "Consignee",
                        Header =  _coreServiceResourceHelper.GetResourceString(ExportObj.UserCulture.ToString(), "Consignee"),
                        exprtType = ExportObj.exprtType,
                        UserCulture = ExportObj.UserCulture
                    };

                    // Call ReportExport service via HTTP
                    var result = await CallReportExportServiceAsync(reportExportList, connString, LogException);
                    return result;
                }
            }
            catch (Exception ex)
            {
                if (LogException == 1)
                {
                    await LogToUtilityServiceAsync(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                }
            }
            return null;
        }

        /// <summary>
        /// Call ReportExport service via HTTP
        /// </summary>
        private async Task<object> CallReportExportServiceAsync(ReportExportList reportExportList, string connString, int LogException)
        {
            try
            {
                var request = new
                {
                    ReportExportList = reportExportList,
                    ConnString = connString,
                    LogException = LogException
                };

                var json = System.Text.Json.JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                string utilityServiceUrl = _configuration["ServiceUrls:UtilitiesService"] ?? "http://localhost:5003";

                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var response = await _httpClient.PostAsync($"{utilityServiceUrl}/api/reportexport/export", content, cts.Token);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return System.Text.Json.JsonSerializer.Deserialize<object>(responseContent);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling ReportExport service");
                return null;
            }
        }

        #endregion

    }
}