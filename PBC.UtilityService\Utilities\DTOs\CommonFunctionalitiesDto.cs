using System;
using System.ComponentModel.DataAnnotations;
using PBC.UtilityService.Utilities.Models;

namespace PBC.UtilityService.Utilities.DTOs
{
    /// <summary>
    /// DTO for GPS Details insertion request
    /// </summary>
    public class InsertGPSDetailsRequest
    {
        [Required]
        public int Company_ID { get; set; }

        [Required]
        public int BranchID { get; set; }

        [Required]
        public int USER_ID { get; set; }

        [Required]
        public int OBJECT_ID { get; set; }

        [Required]
        public int RECORD_ID { get; set; }

        [Required]
        public double LATITUDE { get; set; }

        [Required]
        public double LONGITUDE { get; set; }

        [Required]
        [StringLength(100)]
        public string ActionName { get; set; }

        public bool IsFromMobile { get; set; }

        [Required]
        public int Menu_ID { get; set; }

        public DateTime? LoggedINDate { get; set; }

        public DateTime? LoggedDateTime { get; set; }
    }

    /// <summary>
    /// DTO for Resource request
    /// </summary>
    public class GetResourceRequest
    {
        [Required]
        [StringLength(50)]
        public string CultureValue { get; set; }

        [Required]
        [StringLength(100)]
        public string ResourceKey { get; set; }
    }

    /// <summary>
    /// DTO for Local Time conversion request
    /// </summary>
    public class LocalTimeRequest
    {
        [Required]
        public int BranchID { get; set; }

        [Required]
        public DateTime ServerTime { get; set; }
    }

    /// <summary>
    /// DTO for Load Company request
    /// </summary>
    public class LoadCompanyRequest
    {
        [Required]
        [StringLength(50)]
        public string CompanyType { get; set; }

        public string ConnectionString { get; set; }
    }

    /// <summary>
    /// DTO for Load Branch request
    /// </summary>
    public class LoadBranchRequest
    {
        public bool Active { get; set; } = true;

        [Required]
        public int CompanyID { get; set; }
        public string ConnectionString { get; set; }
    }

    /// <summary>
    /// DTO for Load Branch Locale request
    /// </summary>
    public class LoadBranchLocaleRequest
    {
        public bool Active { get; set; } = true;

        [Required]
        public int CompanyID { get; set; }

        [Required]
        public int UserLanguageID { get; set; }
        public string ConnectionString { get; set; }
    }

    /// <summary>
    /// DTO for Delete Attachments request
    /// </summary>
    public class DeleteAttachmentsRequest
    {
        [Required]
        public Attachements[] Attachments { get; set; }

        [Required]
        [StringLength(500)]
        public string ServerPath { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; }
    }

    /// <summary>
    /// DTO for Upload Attachment request
    /// </summary>
    public class UploadAttachmentRequest
    {
        [Required]
        public Attachements[] Attachments { get; set; }

        [Required]
        public int TransactionId { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        public int CompanyId { get; set; }

        public int DetailId { get; set; }

        public string ConnectionString { get; set; }
    }

    /// <summary>
    ///  Get SR Role Access request
    /// </summary>
    public class GetSRRoleAccessRequest
    {
        public string Name { get; set; }

        public int UserID { get; set; }

        public string Conn { get; set; }
    }

    /// <summary>
    /// DTO for Select All Attachment request
    /// </summary>
    public class SelAllAttachmentRequest
    {
        public SelAllAttachmentList SelAllAttachmentObj { get; set; }

        public string ConnString { get; set; }

        public int LogException { get; set; }

        public string Sidx { get; set; }

        public string Sord { get; set; }

        public int Page { get; set; }

        public int Rows { get; set; }

        public bool _search { get; set; }

        public string Filters { get; set; }
    }

    /// <summary>
    /// DTO for Get Attachment Count request
    /// </summary>
    public class GetAttachmentCountRequest
    {
        [Required]
        public int ObjectID { get; set; }

        [Required]
        public int TransactionID { get; set; }

        public int DetailID { get; set; }

        public string ConnectionString { get; set; }
    }

    /// <summary>
    /// DTO for Convert to Hours request
    /// </summary>
    public class ConvertToHoursRequest
    {
        [Required]
        [Range(0, int.MaxValue)]
        public int Minutes { get; set; }
    }

    /// <summary>
    /// DTO for Get Workflow Role Condition request
    /// </summary>
    public class GetGrpQinconditionRequest
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; } = string.Empty;

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get Working Hours request
    /// </summary>
    public class GetWorkingHoursRequest
    {
        [Required]
        public DateTime CallDate { get; set; }

        [Required]
        public int CompanyId { get; set; }

        [Required]
        public string ConnectionString { get; set; } = string.Empty;

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get Object ID request
    /// </summary>
    public class GetObjectIDRequest
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for Check Party Specific request
    /// </summary>
    public class CheckPartySpecificRequest
    {
        [Required]
        public int PartyId { get; set; }

        [Required]
        public int CallComplexityId { get; set; }

        public int? CallPriorityId { get; set; }

        [Required]
        public int CompanyId { get; set; }

        [Required]
        public string ConnectionString { get; set; } = string.Empty;

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get Region Name request
    /// </summary>
    public class GetRegionNameRequest
    {
        [Required]
        public int UserLanguageId { get; set; }

        [Required]
        public int GeneralLanguageId { get; set; }

        public int? BranchId { get; set; }

        public string ConnectionString { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for Get Status IDs request
    /// </summary>
    public class GetStatusIDsRequest
    {
        [Required]
        public int StatusId { get; set; }

        [Required]
        public int WorkFlowId { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; } = string.Empty;

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get End Step Status ID request
    /// </summary>
    public class GetEndStepStatusIDRequest
    {
        [Required]
        public int WorkflowID { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; }

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get End Step Status Name request
    /// </summary>
    public class GetEndStepStatusNameRequest
    {
        [Required]
        public int WorkflowID { get; set; }

        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; }

        public int LogException { get; set; } = 1;
    }

    /// <summary>
    /// DTO for Get Service Request Query request
    /// </summary>
    public class GetServiceRequestQueryRequest
    {
        [Required]
        [StringLength(500)]
        public string ConnectionString { get; set; }

        public int LogException { get; set; } = 1;

        [Required]
        public int LangID { get; set; }

        [StringLength(10)]
        public string GenLangCode { get; set; } = "en";

        [StringLength(10)]
        public string UserLangCode { get; set; } = "en";

        public int Mode { get; set; } = 0;

        public int User_ID { get; set; } = 0;

        public int Company_ID { get; set; } = 0;

        public int Branch_ID { get; set; } = 0;

        [StringLength(100)]
        public string Sidx { get; set; } = "";

        [StringLength(10)]
        public string Sord { get; set; } = "";

        [StringLength(100)]
        public string DbName { get; set; } = "";
    }

    /// <summary>
    /// Generic response DTO
    /// </summary>
    public class CommonFunctionalitiesResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public T Data { get; set; }
    }

    #region LogSheetExporter DTOs

    /// <summary>
    /// Request for logging to text file
    /// </summary>
    public class LogRequest
    {
        /// <summary>
        /// Exception ID
        /// </summary>
        [Required]
        public int ExId { get; set; }

        /// <summary>
        /// Exception message
        /// </summary>
        [Required]
        public string ExMessage { get; set; } = string.Empty;

        /// <summary>
        /// Exception details
        /// </summary>
        [Required]
        public string ExDetails { get; set; } = string.Empty;

        /// <summary>
        /// Exception stack trace
        /// </summary>
        [Required]
        public string ExStackTrace { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response for logging operations
    /// </summary>
    public class LogResponse
    {
        /// <summary>
        /// Indicates if the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Response message
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for Check for Admin request
    /// </summary>
    public class CheckForAdminRequest
    {
        [Required]
        public int UserId { get; set; }

        [Required]
        public string WorkFlowName { get; set; } = string.Empty;

        [Required]
        public string DBName { get; set; } = string.Empty;

        [Required]
        public string ConnectionString { get; set; } = string.Empty;

        public int LogException { get; set; } = 1;
    }

    #endregion

    #region Export DTOs
    /// <summary>
    /// DTO for Export operations
    /// </summary>
    public class ExportList
    {
        public int Company_ID { get; set; }
        public int? Branch { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? dt1 { get; set; }
        public string? FileName { get; set; }
        public string? Header { get; set; }
        public int exprtType { get; set; }
        public string? UserCulture { get; set; }
        public System.Data.DataTable? DtCriteria { get; set; }
    }

    /// <summary>
    /// DTO for Report Export operations
    /// </summary>
    public class ReportExportList
    {
        public int Company_ID { get; set; }
        public string? Branch { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public System.Data.DataTable? Options { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? Alignment { get; set; }
        public string? FileName { get; set; }
        public string? Header { get; set; }
        public int exprtType { get; set; }
        public string? UserCulture { get; set; }
    }

    /// <summary>
    /// DTO for Dashboard Export operations
    /// </summary>
    public class DashBoardExportList
    {
        public object? Branch { get; set; }
        public int GeneralLanguageID { get; set; }
        public int UserLanguageID { get; set; }
        public int Company_ID { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? Header { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public System.Data.DataTable? dt1 { get; set; }
        public string? UserCulture { get; set; }
        public string? FileName { get; set; }
        public int exprtType { get; set; }
    }

    /// <summary>
    /// DTO for Export Report CR5 operations
    /// </summary>
    public class ExportReportExportCR5List
    {
        public string? FileName { get; set; }
        public string? Branch { get; set; }
        public int Company_ID { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public int exprtType { get; set; }
        public string? Header { get; set; }
        public System.Data.DataTable? Options { get; set; }
        public System.Data.DataSet? selection { get; set; }
        public System.Data.DataTable? Alignment { get; set; }
        public string? UserCulture { get; set; }
    }

    /// <summary>
    /// DTO for Export Report Export1 operations
    /// </summary>
    public class ExportReportExport1List
    {
        public string? FileName { get; set; }
        public string? Branch { get; set; }
        public int Company_ID { get; set; }
        public System.Data.DataTable? dt { get; set; }
        public int exprtType { get; set; }
        public string? Header { get; set; }
        public System.Data.DataTable? Options { get; set; }
        public System.Data.DataSet? selection { get; set; }
        public System.Data.DataTable? Alignment { get; set; }
        public string? UserCulture { get; set; }
    }
    #endregion
}
