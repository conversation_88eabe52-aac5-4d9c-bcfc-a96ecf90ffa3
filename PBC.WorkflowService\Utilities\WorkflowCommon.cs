using System;
using System.Data;
using System.Data.SqlClient;
using PBC.WorkflowService.Models;

namespace PBC.WorkflowService.Utilities
{
    /// <summary>
    /// Utility class for common workflow operations
    /// </summary>
    public class WorkflowCommon
    {
        private readonly ILogger<WorkflowCommon> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowCommon"/> class.
        /// </summary>
        /// <param name="logger"></param>
        public WorkflowCommon(ILogger<WorkflowCommon> logger)
        {
            _logger = logger;
        }

        #region CheckPreffixSuffix
        /// <summary>
        /// Check if prefix/suffix configuration exists for the given parameters.
        /// </summary>
        /// <param name="companyID"></param>
        /// <param name="branchID"></param>
        /// <param name="objectName"></param>
        /// <param name="dbName"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public bool CheckPreffixSuffix(int companyID, int branchID, string objectName, string dbName, string constring)
        {
            bool flag = false;
            int objectID = GetObjectID(objectName, dbName, constring);

            try
            {
                // Construct the connection string with the provided database name (dbName)
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Define the current date to use in both queries
                    DateTime currentDate = DateTime.Now;

                    // First Query: Check PrefixSuffix with specific Branch ID
                    string query1 = @"SELECT COUNT(1) 
                              FROM WF_PrefixSuffix 
                              WHERE Branch_ID = @BranchID 
                                AND Object_ID = @ObjectID 
                                AND FromDate <= @CurrentDate 
                                AND ToDate >= @CurrentDate";

                    using (SqlCommand cmd = new SqlCommand(query1, conn))
                    {
                        cmd.Parameters.AddWithValue("@BranchID", branchID);
                        cmd.Parameters.AddWithValue("@ObjectID", objectID);
                        cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                        int count = Convert.ToInt32(cmd.ExecuteScalar());
                        flag = count > 0;
                    }

                    // Second Query: Check PrefixSuffix with null Branch ID if the first check fails
                    if (!flag)
                    {
                        string query2 = @"SELECT COUNT(1) 
                                  FROM WF_PrefixSuffix 
                                  WHERE Company_ID = @CompanyID 
                                    AND Branch_ID IS NULL 
                                    AND Object_ID = @ObjectID 
                                    AND FromDate <= @CurrentDate 
                                    AND ToDate >= @CurrentDate";

                        using (SqlCommand cmd = new SqlCommand(query2, conn))
                        {
                            cmd.Parameters.AddWithValue("@CompanyID", companyID);
                            cmd.Parameters.AddWithValue("@ObjectID", objectID);
                            cmd.Parameters.AddWithValue("@CurrentDate", currentDate);

                            int count = Convert.ToInt32(cmd.ExecuteScalar());
                            flag = count > 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Use LogSheetExporter for error logging in static utility classes
                // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in CheckPreffixSuffix: {ErrorMessage}", ex.Message);
            }

            return flag;
        }

        #region GetWorkFlowID
        /// <summary>
        /// GetWorkFlowID
        /// </summary>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public int GetWorkFlowID(string WorkFlowName, string DBName, string constring, int LogException)
        {
            int workflowID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to get the WorkFlow_ID based on WorkFlowName
                    string query = @"SELECT TOP 1 WorkFlow_ID 
                             FROM GNM_WorkFlow 
                             WHERE WorkFlow_Name = @WorkFlowName";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        // Add the parameter to prevent SQL injection
                        cmd.Parameters.AddWithValue("@WorkFlowName", WorkFlowName);

                        // Execute the query and get the WorkFlow_ID
                        object result = cmd.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                        {
                            workflowID = Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is 0
                if (LogException == 0)
                {
                    // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetWorkFlowID: {ErrorMessage}", ex.Message);
                }
            }

            return workflowID;
        }
        #endregion

        /// <summary>
        /// GetStepLinkForInsert
        /// </summary>
        /// <param name="constring"></param>
        /// <param name="companyID"></param>
        /// <param name="workFlowID"></param>
        /// <param name="stepID"></param>
        /// <param name="ActionID"></param>
        /// <returns></returns>
        public List<WF_WFStepLink> GetStepLinkForInsert(string constring, int companyID, int workFlowID, int stepID, int ActionID)
        {
            List<WF_WFStepLink> list = new List<WF_WFStepLink>();

            try
            {
                using (SqlConnection con = new SqlConnection(constring))
                {
                    con.Open();

                    // First query to get initial WF_WFStepLink records
                    string query1 = @"
                SELECT WFStepLink_ID, FrmWFSteps_ID, ToWFSteps_ID, WorkFlow_ID, Company_ID, WFAction_ID 
                FROM GNM_WFStepLink 
                WHERE FrmWFSteps_ID = @StepID AND WorkFlow_ID = @WorkFlowID AND Company_ID = @CompanyID AND WFAction_ID = @ActionID";

                    using (SqlCommand cmd1 = new SqlCommand(query1, con))
                    {
                        cmd1.Parameters.AddWithValue("@StepID", stepID);
                        cmd1.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                        cmd1.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd1.Parameters.AddWithValue("@ActionID", ActionID);

                        using (SqlDataReader reader1 = cmd1.ExecuteReader())
                        {
                            while (reader1.Read())
                            {
                                WF_WFStepLink stepLink = new WF_WFStepLink
                                {
                                    WFStepLink_ID = Convert.ToInt32(reader1["WFStepLink_ID"]),
                                    FrmWFSteps_ID = Convert.ToInt32(reader1["FrmWFSteps_ID"]),
                                    ToWFSteps_ID = Convert.ToInt32(reader1["ToWFSteps_ID"]),
                                    WorkFlow_ID = Convert.ToInt32(reader1["WorkFlow_ID"]),
                                    Company_ID = Convert.ToInt32(reader1["Company_ID"]),
                                    WFAction_ID = Convert.ToInt32(reader1["WFAction_ID"])
                                };

                                list.Add(stepLink);

                                // Second query to get related WF_WFStepLink records based on ToWFSteps_ID
                                string query2 = @"
                            SELECT WFStepLink_ID, FrmWFSteps_ID, ToWFSteps_ID, WorkFlow_ID, Company_ID, WFAction_ID 
                            FROM GNM_WFStepLink 
                            WHERE FrmWFSteps_ID = @ToStepID AND WorkFlow_ID = @WorkFlowID AND Company_ID = @CompanyID";

                                using (SqlCommand cmd2 = new SqlCommand(query2, con))
                                {
                                    cmd2.Parameters.AddWithValue("@ToStepID", stepLink.ToWFSteps_ID);
                                    cmd2.Parameters.AddWithValue("@WorkFlowID", workFlowID);
                                    cmd2.Parameters.AddWithValue("@CompanyID", companyID);

                                    using (SqlDataReader reader2 = cmd2.ExecuteReader())
                                    {
                                        while (reader2.Read())
                                        {
                                            WF_WFStepLink additionalStepLink = new WF_WFStepLink
                                            {
                                                WFStepLink_ID = Convert.ToInt32(reader2["WFStepLink_ID"]),
                                                FrmWFSteps_ID = Convert.ToInt32(reader2["FrmWFSteps_ID"]),
                                                ToWFSteps_ID = Convert.ToInt32(reader2["ToWFSteps_ID"]),
                                                WorkFlow_ID = Convert.ToInt32(reader2["WorkFlow_ID"]),
                                                Company_ID = Convert.ToInt32(reader2["Company_ID"]),
                                                WFAction_ID = Convert.ToInt32(reader2["WFAction_ID"])
                                            };
                                            list.Add(additionalStepLink);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in GetStepLinkForInsert: {ErrorMessage}", ex.Message);
            }

            return list;
        }

        /// <summary>
        /// GetListOfStaffForaRoleID
        /// </summary>
        /// <param name="constring"></param>
        /// <param name="companyID"></param>
        /// <param name="roleID"></param>
        /// <param name="transactionValue"></param>
        /// <param name="stepID"></param>
        /// <param name="actionID"></param>
        /// <param name="UserLanguageID"></param>
        /// <returns></returns>
        public List<ResultForAction> GetListOfStaffForaRoleID(string constring, int companyID, int roleID, int transactionValue, int stepID, int actionID, int UserLanguageID)
        {
            List<ResultForAction> resultList = new List<ResultForAction>();
            bool isVersionEnabled = false;
            int externalCompanyID = 0;

            try
            {

                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Fetch WFRole_ExternalCompany_ID for the given roleID
                    string query1 = @"SELECT WFRole_ExternalCompany_ID 
                              FROM GNM_WFRole 
                              WHERE WFRole_ID = @RoleID";

                    using (SqlCommand cmd = new SqlCommand(query1, conn))
                    {
                        cmd.Parameters.AddWithValue("@RoleID", roleID);
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            externalCompanyID = Convert.ToInt32(result);
                        }
                    }

                    // Check if IsVersionEnabled flag is set
                    string query2 = @"SELECT ISNULL(IsVersionEnabled, 0) 
                              FROM GNM_WFStepLink 
                              WHERE Company_ID = @CompanyID 
                              AND FrmWFSteps_ID = @StepID 
                              AND WFAction_ID = @ActionID";

                    using (SqlCommand cmd = new SqlCommand(query2, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyID", companyID);
                        cmd.Parameters.AddWithValue("@StepID", stepID);
                        cmd.Parameters.AddWithValue("@ActionID", actionID);
                        object result = cmd.ExecuteScalar();
                        if (result != null)
                        {
                            isVersionEnabled = Convert.ToBoolean(result);
                        }
                    }

                    // Fetch user details based on UserLanguageID
                    string query3;
                    if (UserLanguageID == 0)
                    {
                        query3 = @"SELECT DISTINCT a.WFRole_ID, b.User_ID, b.User_Name, b.User_LoginID
                           FROM GNM_WFRoleUser a
                           INNER JOIN GNM_User b ON a.UserID = b.User_ID
                           WHERE a.WFRole_ID = @RoleID 
                           AND b.Company_ID = @ExtCompID 
                           AND b.User_IsActive = 1";
                    }
                    else
                    {
                        query3 = @"SELECT DISTINCT a.WFRole_ID, b.User_ID, l.User_Name, b.User_LoginID
                           FROM GNM_WFRoleUser a
                           INNER JOIN GNM_User b ON a.UserID = b.User_ID
                           INNER JOIN GNM_UserLocale l ON b.User_ID = l.User_ID
                           WHERE a.WFRole_ID = @RoleID 
                           AND b.Company_ID = @ExtCompID 
                           AND b.User_IsActive = 1 
                           AND l.Language_ID = @UserLanguageID";
                    }

                    using (SqlCommand cmd = new SqlCommand(query3, conn))
                    {
                        cmd.Parameters.AddWithValue("@RoleID", roleID);
                        cmd.Parameters.AddWithValue("@ExtCompID", externalCompanyID);

                        if (UserLanguageID != 0)
                        {
                            cmd.Parameters.AddWithValue("@UserLanguageID", UserLanguageID);
                        }

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ResultForAction resultForAction = new ResultForAction
                                {
                                    ID = reader.GetInt32(reader.GetOrdinal("User_ID")),
                                    Name = reader.GetString(reader.GetOrdinal("User_Name")),
                                    isVersionEnabled = isVersionEnabled
                                };

                                resultList.Add(resultForAction);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception
                // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in GetListOfStaffForaRoleID: {ErrorMessage}", ex.Message);
            }

            return resultList;
        }

        /// <summary>
        /// GetAssociatedRolesForaStep
        /// </summary>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <param name="companyID"></param>
        /// <param name="stepID"></param>
        /// <param name="actionID"></param>
        /// <param name="workFlowID"></param>
        /// <param name="UserLanguageID"></param>
        /// <returns></returns>
        public List<ResultForAction> GetAssociatedRolesForaStep(string constring, int LogException, int companyID, int stepID, int actionID, int workFlowID, int UserLanguageID)
        {
            List<ResultForAction> resultList = new List<ResultForAction>();
            List<int> toWFStepsIDList = new List<int>();
            bool isVersionEnabled = false;

            try
            {
                using (SqlConnection conn = new SqlConnection(constring))
                {
                    conn.Open();

                    // Query to check if the version is enabled
                    string versionCheckQuery = @"
                SELECT TOP 1 IsVersionEnabled 
                FROM GNM_WFStepLink 
                WHERE Company_ID = @CompanyID 
                  AND FrmWFSteps_ID = @StepID 
                  AND WFAction_ID = @ActionID";

                    using (SqlCommand versionCmd = new SqlCommand(versionCheckQuery, conn))
                    {
                        versionCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        versionCmd.Parameters.AddWithValue("@StepID", stepID);
                        versionCmd.Parameters.AddWithValue("@ActionID", actionID);

                        object versionResult = versionCmd.ExecuteScalar();
                        isVersionEnabled = versionResult != null && versionResult != DBNull.Value && Convert.ToBoolean(versionResult);
                    }

                    // Fetch all `ToWFSteps_ID` related to given `FrmWFSteps_ID`, `Company_ID`, and `WorkFlow_ID`
                    string toWFStepsQuery = @"
                SELECT ToWFSteps_ID 
                FROM GNM_WFStepLink 
                WHERE FrmWFSteps_ID = @StepID 
                  AND Company_ID = @CompanyID 
                  AND WFAction_ID = @ActionID 
                  AND WorkFlow_ID = @WorkFlowID";

                    using (SqlCommand toWFStepsCmd = new SqlCommand(toWFStepsQuery, conn))
                    {
                        toWFStepsCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        toWFStepsCmd.Parameters.AddWithValue("@StepID", stepID);
                        toWFStepsCmd.Parameters.AddWithValue("@ActionID", actionID);
                        toWFStepsCmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);

                        using (SqlDataReader reader = toWFStepsCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                toWFStepsIDList.Add(reader.GetInt32(0));
                            }
                        }
                    }

                    // If UserLanguageID is 0, join with WF_WFRole table, otherwise join with WF_WFRoleLocale
                    string roleQuery = UserLanguageID == 0 ?
                        @"
                SELECT DISTINCT b.WFRole_ID AS ID, b.WFRole_Name AS Name
                FROM GNM_WFStepLink a
                INNER JOIN GNM_WFRole b ON a.Addresse_WFRole_ID = b.WFRole_ID
                WHERE a.ToWFSteps_ID IN (@ToWFStepsList) 
                  AND a.Company_ID = @CompanyID 
                  AND a.WorkFlow_ID = @WorkFlowID"
                        :
                        @"
                SELECT DISTINCT b.WFRole_ID AS ID, b.WFRole_Name AS Name
                FROM GNM_WFStepLink a
                INNER JOIN GNM_WFRoleLocale b ON a.Addresse_WFRole_ID = b.WFRole_ID
                WHERE a.ToWFSteps_ID IN (@ToWFStepsList) 
                  AND b.Language_ID = @UserLanguageID 
                  AND a.Company_ID = @CompanyID 
                  AND a.WorkFlow_ID = @WorkFlowID";

                    using (SqlCommand roleCmd = new SqlCommand(roleQuery, conn))
                    {
                        // Prepare parameter for `ToWFStepsList` as a comma-separated string
                        var toWFStepsListParam = string.Join(",", toWFStepsIDList);
                        roleCmd.Parameters.AddWithValue("@ToWFStepsList", toWFStepsListParam);
                        roleCmd.Parameters.AddWithValue("@CompanyID", companyID);
                        roleCmd.Parameters.AddWithValue("@WorkFlowID", workFlowID);

                        if (UserLanguageID != 0)
                        {
                            roleCmd.Parameters.AddWithValue("@UserLanguageID", UserLanguageID);
                        }

                        using (SqlDataReader reader = roleCmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ResultForAction result = new ResultForAction
                                {
                                    ID = reader.GetInt32(0),
                                    Name = reader.GetString(1),
                                    isVersionEnabled = isVersionEnabled
                                };
                                resultList.Add(result);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log exception if LogException flag is 0
                if (LogException == 0)
                {
                    // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                    _logger.LogError(ex, "Error in GetAssociatedRolesForaStep: {ErrorMessage}", ex.Message);
                }
            }

            return resultList;
        }
        #endregion

        #region GetRolesForActions
        /// <summary>
        /// GetRolesForActions
        /// </summary>
        /// <param name="WFCurrentStepID"></param>
        /// <param name="ActionID"></param>
        /// <param name="TransactionID"></param>
        /// <param name="CompanyID"></param>
        /// <param name="WorkFlowName"></param>
        /// <param name="DBName"></param>
        /// <param name="UserLanguageID"></param>
        /// <param name="constring"></param>
        /// <param name="LogException"></param>
        /// <returns></returns>
        public dynamic GetRolesForActions(int WFCurrentStepID, int ActionID, int TransactionID, int CompanyID, string WorkFlowName, string DBName, int UserLanguageID, string constring, int LogException)
        {
            object obj = null;
            object result = obj;
            int num = 0;
            List<WF_WFStepLink> list = null;
            object obj2 = obj;
            bool flag = true;
            List<ResultForAction> list2 = new List<ResultForAction>();
            try
            {
                num = GetWorkFlowID(WorkFlowName, DBName, constring, LogException);
                list = GetStepLinkForInsert(constring, CompanyID, num, WFCurrentStepID, ActionID);
                if (list != null && list.Count > 0)
                {
                    foreach (WF_WFStepLink item in list)
                    {
                        if (item.Addresse_Flag == 1)
                        {
                            list2.AddRange(GetListOfStaffForaRoleID(constring, CompanyID, item.Addresse_WFRole_ID.Value, 0, WFCurrentStepID, ActionID, UserLanguageID));
                            flag = false;
                        }
                        else
                        {
                            list2.AddRange(GetAssociatedRolesForaStep(constring, LogException, CompanyID, WFCurrentStepID, ActionID, num, UserLanguageID));
                            flag = true;
                        }
                    }

                    IQueryable<ResultForAction> list3 = list2.AsQueryable();
                    result = new
                    {
                        Result = (flag ? "Role" : "Individual"),
                        ResultList = (from res in list2
                                      orderby res.Name
                                      select new { res.ID, res.isVersionEnabled, res.Name }).Distinct().ToArray(),
                        CallDate = DateTime.Now.ToShortDateString(),
                        CallTime = DateTime.Now.ToShortTimeString()
                    };
                }
                else
                {
                    result = new
                    {
                        Result = "End",
                        CallDate = DateTime.Now.ToString("dd-MMM-yyyy") + ' ' + DateTime.Now.ToString("HH:mm"),
                        CallTime = DateTime.Now.ToShortTimeString()
                    };
                }
            }
            catch (Exception ex)
            {
                // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite.ToString(), ex.StackTrace);
                _logger.LogError(ex, "Error in GetRolesForActions: {ErrorMessage}", ex.Message);
            }

            return result;
        }

        /// <summary>
        /// GetObjectID
        /// </summary>
        /// <param name="name"></param>
        /// <param name="dbName"></param>
        /// <param name="constring"></param>
        /// <returns></returns>
        public int GetObjectID(string name, string dbName, string constring)
        {
            int result = 0;

            try
            {

                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(constring)
                {
                    InitialCatalog = dbName
                };

                using (SqlConnection conn = new SqlConnection(builder.ToString()))
                {
                    conn.Open();

                    // Query to get the Object_ID based on Object_Name
                    string query = @"SELECT TOP 1 Object_ID 
                             FROM WF_Object 
                             WHERE UPPER(Object_Name) = UPPER(@ObjectName)";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@ObjectName", name);

                        // Execute the query and retrieve the result
                        object obj = cmd.ExecuteScalar();
                        if (obj != null && obj != DBNull.Value)
                        {
                            result = Convert.ToInt32(obj);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Use LogSheetExporter for error logging in static utility classes
                // LogSheetExporter.LogToTextFile(ex.HResult, ex.GetType().FullName + ":" + ex.Message, ex.TargetSite?.ToString() ?? "", ex.StackTrace ?? "");
                _logger.LogError(ex, "Error in GetObjectID: {ErrorMessage}", ex.Message);
            }

            return result;
        }
        #endregion
    }
}