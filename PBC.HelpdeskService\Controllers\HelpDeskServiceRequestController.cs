﻿using Microsoft.AspNetCore.Mvc;
using PBC.HelpdeskService.Services;
using PBC.HelpdeskService.Models;

namespace PBC.HelpdeskService.Controllers
{

    /// <summary>
    /// Controller for Help Desk Request Page operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HelpDeskServiceRequestController : Controller
    {
        private readonly IHelpDeskUserLandingPageServices _helpDeskUserLandingPageServices;
        private readonly IHelpDeskServiceRequestServices _helpDeskServiceRequestServices;
        private readonly IConfiguration _configuration;
        private readonly ILogger<HelpDeskServiceRequestController> _logger;

        public HelpDeskServiceRequestController(
            IHelpDeskUserLandingPageServices helpDeskUserLandingPageServices,
            IHelpDeskServiceRequestServices helpDeskServiceRequestServices,
            IConfiguration configuration,
            ILogger<HelpDeskServiceRequestController> logger)
        {
            _helpDeskUserLandingPageServices = helpDeskUserLandingPageServices;
            _helpDeskServiceRequestServices = helpDeskServiceRequestServices;
            _configuration = configuration;
            _logger = logger;
        }
        #region ::: InsertSR :::
        /// <summary>
        /// Insert Service Request
        /// </summary>
        /// <param name="request">Service request data</param>
        /// <returns>Insert result</returns>
        [HttpPost("insert-sr")]
        public async Task<IActionResult> InsertSR([FromBody] InsertSRRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/insert-sr");

                var result = await _helpDeskServiceRequestServices.InsertSR(
                    request.ConnectionString ?? connectionString,
                    request.Reopen,
                    request.Data,
                    request.BranchID,
                    request.UserLanguageCode,
                    request.RequestParams,
                    request.HDAttachmentData,
                    request.Path,
                    request.ConnString ?? connectionString,
                    request.LogException,
                    request.Company_ID,
                    request.User_ID,
                    request.HelpDesk,
                    request.HelpLineNumber,
                    request.Language_ID,
                    request.Employee_ID,
                    request.MenuID,
                    request.HolidayDetails,
                    request.IsFromWebAPI
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InsertSR");
                return StatusCode(500, "An error occurred while inserting service request");
            }
        }
        #endregion

        #region ::: InitialMode :::
        /// <summary>
        /// Get initial mode data
        /// </summary>
        /// <param name="request">Initial mode request</param>
        /// <returns>Initial mode data</returns>
        [HttpPost("initial-mode")]
        public IActionResult InitialMode([FromBody] InitialModeRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/initial-mode");

                var result = _helpDeskServiceRequestServices.InitialMode(
                    request.UserEmployeeId,
                    request.CompanyId,
                    request.UserCulture,
                    request.ConnectionString ?? connectionString,
                    request.UnRegServiceRequestId,
                    request.PartyId,
                    request.ModelId,
                    request.SerialNumber,
                    request.RequestDesc,
                    request.ModelName,
                    request.Unique,
                    request.ServiceRequestId,
                    request.Reopen,
                    request.Mode,
                    request.StatusId,
                    request.CompanyIdAlt
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in InitialMode");
                return StatusCode(500, "An error occurred while getting initial mode data");
            }
        }
        #endregion

        #region ::: SelectSRDetails :::
        /// <summary>
        /// Select Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("select-sr-details")]
        public IActionResult SelectSRDetails([FromBody] SelectSRDetailsRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-sr-details");

                var result = _helpDeskServiceRequestServices.SelectSRDetails(
                    request.User,
                    request.ServiceRequestID,
                    request.ChildTicketSequenceID,
                    request.BranchID,
                    request.ConnectionString ?? connectionString,
                    request.IsReopen,
                    request.IsOemDashboard,
                    request.OemCompanyId,
                    request.UserCulture,
                    request.GeneralLanguageCode,
                    request.UserLanguageCode,
                    request.MenuID,
                    request.LoggedInDateTime
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectSRDetails");
                return StatusCode(500, "An error occurred while selecting service request details");
            }
        }
        #endregion
        #region ::: GetProductDetails :::
        /// <summary>
        /// Get Product Details
        /// </summary>
        /// <param name="request">Product details request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-product-details")]
        public IActionResult GetProductDetails([FromBody] GetProductDetailsFList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-details");

                var result = _helpDeskServiceRequestServices.GetProductDetails(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetails");
                return StatusCode(500, "An error occurred while getting product details");
            }
        }
        #endregion

        #region ::: GetActions :::
        /// <summary>
        /// Get Actions
        /// </summary>
        /// <param name="request">Actions request</param>
        /// <returns>Actions data</returns>
        [HttpPost("get-actions")]
        public async Task<IActionResult> GetActions([FromBody] GetActionsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-actions");

                var result = await _helpDeskServiceRequestServices.GetActions(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetActions");
                return StatusCode(500, "An error occurred while getting actions");
            }
        }
        #endregion

        #region ::: EditSR :::
        /// <summary>
        /// Edit Service Request
        /// </summary>
        /// <param name="request">Edit SR request</param>
        /// <returns>Edit result</returns>
        [HttpPost("edit-sr")]
        public IActionResult EditSR([FromBody] EditSRRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/edit-sr");

                var result = _helpDeskServiceRequestServices.EditSR(
                    request.Data,
                    request.BranchID,
                    request.UserLanguageCode,
                    request.RequestParams,
                    request.HDAttachmentData,
                    request.LoginCompanyID,
                    request.ConnString ?? connectionString,
                    request.LogException,
                    request.Company_ID,
                    request.User_ID,
                    request.HelpDesk,
                    request.HelpLineNumber,
                    request.Language_ID,
                    request.Employee_ID,
                    request.MenuID,
                    request.HolidayDetails,
                    request.IsFromWebAPI
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in EditSR");
                return StatusCode(500, "An error occurred while editing service request");
            }
        }
        #endregion
        #region ::: GetSRDetails :::
        /// <summary>
        /// Get Service Request Details
        /// </summary>
        /// <param name="request">SR details request</param>
        /// <returns>SR details</returns>
        [HttpPost("get-sr-details")]
        public async Task<IActionResult> GetSRDetails([FromBody] GetSRDetailsRequest request, [FromQuery] string connectionString)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-sr-details");

                var result = await _helpDeskServiceRequestServices.GetSRDetails(
                    request.User,
                    request.ServiceRequestID,
                    request.ChildTicket_Sequence_ID,
                    request.User_ID,
                    request.Company_ID,
                    request.Branch_ID,
                    request.UserCulture,
                    request.IsReopen.ToString(),
                    request.ConnString ?? connectionString,
                    "",
                    request.GeneralLanguageCode,
                    request.UserLanguageCode
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSRDetails");
                return StatusCode(500, "An error occurred while getting service request details");
            }
        }
        #endregion

        #region ::: GetDetails :::
        /// <summary>
        /// Get Details for Date
        /// </summary>
        /// <param name="request">Get details request</param>
        /// <returns>Date details</returns>
        [HttpPost("get-details")]
        public IActionResult GetDetails([FromBody] GetDetailsRequest request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-details");

                var result = _helpDeskServiceRequestServices.GetDetails(
                    request.CallDate,
                    request.CompanyID,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDetails");
                return StatusCode(500, "An error occurred while getting details");
            }
        }
        #endregion
        #region ::: loadMasters :::
        /// <summary>
        /// Load Masters
        /// </summary>
        /// <param name="request">Load masters request</param>
        /// <returns>Masters data</returns>
        [HttpPost("load-masters")]
        public IActionResult LoadMasters([FromBody] loadMastersList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/load-masters");

                var result = _helpDeskServiceRequestServices.loadMasters(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadMasters");
                return StatusCode(500, "An error occurred while loading masters");
            }
        }
        #endregion

        #region ::: loadIssueSubArea :::
        /// <summary>
        /// Load Issue Sub Area
        /// </summary>
        /// <param name="request">Load issue sub area request</param>
        /// <returns>Issue sub area data</returns>
        [HttpPost("load-issue-sub-area")]
        public IActionResult LoadIssueSubArea([FromBody] loadIssueSubAreaList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/load-issue-sub-area");

                var result = _helpDeskServiceRequestServices.loadIssueSubArea(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadIssueSubArea");
                return StatusCode(500, "An error occurred while loading issue sub area");
            }
        }
        #endregion

        #region ::: GetPartyDetailsbyID :::
        /// <summary>
        /// Get Party Details by ID
        /// </summary>
        /// <param name="request">Get party details request</param>
        /// <returns>Party details</returns>
        [HttpPost("get-party-details-by-id")]
        public IActionResult GetPartyDetailsbyID([FromBody] GetPartyDetailsbyIDEList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-party-details-by-id");

                var result = _helpDeskServiceRequestServices.GetPartyDetailsbyID(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPartyDetailsbyID");
                return StatusCode(500, "An error occurred while getting party details");
            }
        }
        #endregion

        #region ::: SelectPartyDetailGrid :::
        /// <summary>
        /// Select Party Detail Grid
        /// </summary>
        /// <param name="request">Select party detail grid request</param>
        /// <returns>Party detail grid data</returns>
        [HttpPost("select-party-detail-grid")]
        public IActionResult SelectPartyDetailGrid([FromBody] SelectPartyDetailGridEList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-party-detail-grid");

                var result = _helpDeskServiceRequestServices.SelectPartyDetailGrid(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectPartyDetailGrid");
                return StatusCode(500, "An error occurred while selecting party detail grid");
            }
        }
        #endregion

        #region ::: getProductUniqueNumber :::
        /// <summary>
        /// Get Product Unique Number
        /// </summary>
        /// <param name="request">Get product unique number request</param>
        /// <returns>Product unique number</returns>
        [HttpPost("get-product-unique-number")]
        public IActionResult GetProductUniqueNumber([FromBody] getProductUniqueNumberFList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-unique-number");

                var result = _helpDeskServiceRequestServices.getProductUniqueNumber(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductUniqueNumber");
                return StatusCode(500, "An error occurred while getting product unique number");
            }
        }
        #endregion

        #region ::: getProductWarranty :::
        /// <summary>
        /// Get Product Warranty
        /// </summary>
        /// <param name="request">Get product warranty request</param>
        /// <returns>Product warranty data</returns>
        [HttpPost("get-product-warranty")]
        public IActionResult GetProductWarranty([FromBody] getProductWarrantyList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-warranty");

                var result = _helpDeskServiceRequestServices.getProductWarranty(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductWarranty");
                return StatusCode(500, "An error occurred while getting product warranty");
            }
        }
        #endregion
        #region ::: ValidateReading :::
        /// <summary>
        /// Validate Reading
        /// </summary>
        /// <param name="request">Validate reading request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-reading")]
        public IActionResult ValidateReading([FromBody] ValidateReadingList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/validate-reading");

                var result = _helpDeskServiceRequestServices.ValidateReading(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateReading");
                return StatusCode(500, "An error occurred while validating reading");
            }
        }
        #endregion

        #region ::: ValidateSerialNumber :::
        /// <summary>
        /// Validate Serial Number
        /// </summary>
        /// <param name="request">Validate serial number request</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate-serial-number")]
        public IActionResult ValidateSerialNumber([FromBody] ValidateSerialNumberList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/validate-serial-number");

                var result = _helpDeskServiceRequestServices.ValidateSerialNumber(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateSerialNumber");
                return StatusCode(500, "An error occurred while validating serial number");
            }
        }
        #endregion

        #region ::: getBrandProductType :::
        /// <summary>
        /// Get Brand Product Type
        /// </summary>
        /// <param name="request">Get brand product type request</param>
        /// <returns>Brand product type data</returns>
        [HttpPost("get-brand-product-type")]
        public IActionResult GetBrandProductType([FromBody] getBrandProductTypecList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-brand-product-type");

                var result = _helpDeskServiceRequestServices.getBrandProductType(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetBrandProductType");
                return StatusCode(500, "An error occurred while getting brand product type");
            }
        }
        #endregion

        #region ::: GetSerialNumberForModelforDealer :::
        /// <summary>
        /// Get Serial Number For Model for Dealer
        /// </summary>
        /// <param name="request">Get serial number for model for dealer request</param>
        /// <returns>Serial number data</returns>
        [HttpPost("get-serial-number-for-model-for-dealer")]
        public IActionResult GetSerialNumberForModelforDealer([FromBody] GetSerialNumberForModelforDealerList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-serial-number-for-model-for-dealer");

                var result = _helpDeskServiceRequestServices.GetSerialNumberForModelforDealer(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSerialNumberForModelforDealer");
                return StatusCode(500, "An error occurred while getting serial number for model for dealer");
            }
        }
        #endregion

        #region ::: getCustomerDetailsByPhone :::
        /// <summary>
        /// Get Customer Details by Phone
        /// </summary>
        /// <param name="request">Get customer details by phone request</param>
        /// <returns>Customer details</returns>
        [HttpPost("get-customer-details-by-phone")]
        public IActionResult GetCustomerDetailsByPhone([FromBody] getCustomerDetailsByPhoneList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-customer-details-by-phone");

                var result = _helpDeskServiceRequestServices.getCustomerDetailsByPhone(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerDetailsByPhone");
                return StatusCode(500, "An error occurred while getting customer details by phone");
            }
        }
        #endregion

        #region ::: getCustomerDetailsByEmail :::
        /// <summary>
        /// Get Customer Details by Email
        /// </summary>
        /// <param name="request">Get customer details by email request</param>
        /// <returns>Customer details</returns>
        [HttpPost("get-customer-details-by-email")]
        public IActionResult GetCustomerDetailsByEmail([FromBody] getCustomerDetailsByEmailCList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-customer-details-by-email");

                var result = _helpDeskServiceRequestServices.getCustomerDetailsByEmail(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerDetailsByEmail");
                return StatusCode(500, "An error occurred while getting customer details by email");
            }
        }
        #endregion
        #region ::: ContactPersonMasterSave :::
        /// <summary>
        /// Contact Person Master Save
        /// </summary>
        /// <param name="request">Contact person master save request</param>
        /// <returns>Save result</returns>
        [HttpPost("contact-person-master-save")]
        public IActionResult ContactPersonMasterSave([FromBody] ContactPersonMasterSaveList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/contact-person-master-save");

                var result = _helpDeskServiceRequestServices.ContactPersonMasterSave(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ContactPersonMasterSave");
                return StatusCode(500, "An error occurred while saving contact person master");
            }
        }
        #endregion

        #region ::: GetAllProductDetailsForDealer :::
        /// <summary>
        /// Get All Product Details For Dealer
        /// </summary>
        /// <param name="request">Get all product details for dealer request</param>
        /// <returns>Product details</returns>
        [HttpPost("get-all-product-details-for-dealer")]
        public IActionResult GetAllProductDetailsForDealer([FromBody] GetAllProductDetailsForDealerList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-all-product-details-for-dealer");

                var result = _helpDeskServiceRequestServices.GetAllProductDetailsForDealer(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllProductDetailsForDealer");
                return StatusCode(500, "An error occurred while getting all product details for dealer");
            }
        }
        #endregion

        #region ::: GetRolesForActions :::
        /// <summary>
        /// Get Roles For Actions
        /// </summary>
        /// <param name="request">Get roles for actions request</param>
        /// <returns>Roles data</returns>
        [HttpPost("get-roles-for-actions")]
        public async Task<IActionResult> GetRolesForActions([FromBody] GetRolesForActionsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string helpDesk = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-roles-for-actions");

                var result = await _helpDeskServiceRequestServices.GetRolesForActions(
                    request,
                    connectionString,
                    logException,
                    helpDesk
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetRolesForActions");
                return StatusCode(500, "An error occurred while getting roles for actions");
            }
        }
        #endregion

        #region ::: GetMovementofWorkFlow :::
        /// <summary>
        /// Get Movement of Work Flow
        /// </summary>
        /// <param name="request">Get movement of work flow request</param>
        /// <returns>Work flow movement data</returns>
        [HttpPost("get-movement-of-work-flow")]
        public IActionResult GetMovementofWorkFlow([FromBody] GetMovementofWorkFlowList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string helpDesk = "", [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-movement-of-work-flow");

                var result = _helpDeskServiceRequestServices.GetMovementofWorkFlow(
                    request,
                    connectionString,
                    logException,
                    helpDesk,
                    sidx,
                    sord,
                    page,
                    rows
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMovementofWorkFlow");
                return StatusCode(500, "An error occurred while getting movement of work flow");
            }
        }
        #endregion

        #region ::: GetMovementofWorkFlowforAll :::
        /// <summary>
        /// Get Movement of Work Flow for All
        /// </summary>
        /// <param name="request">Get movement of work flow for all request</param>
        /// <returns>Work flow movement data for all</returns>
        [HttpPost("get-movement-of-work-flow-for-all")]
        public IActionResult GetMovementofWorkFlowforAll([FromBody] GetMovementofWorkFlowforAllList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string helpDesk = "", [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-movement-of-work-flow-for-all");

                var result = _helpDeskServiceRequestServices.GetMovementofWorkFlowforAll(
                    request,
                    connectionString,
                    logException,
                    helpDesk,
                    sidx,
                    sord,
                    page,
                    rows
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetMovementofWorkFlowforAll");
                return StatusCode(500, "An error occurred while getting movement of work flow for all");
            }
        }
        #endregion

        #region ::: SelHDProductDetails :::
        /// <summary>
        /// Select HD Product Details
        /// </summary>
        /// <param name="request">Select HD product details request</param>
        /// <returns>HD product details</returns>
        [HttpPost("sel-hd-product-details")]
        public IActionResult SelHDProductDetails([FromBody] SelHDProductDetailsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/sel-hd-product-details");

                var result = _helpDeskServiceRequestServices.SelHDProductDetails(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows,
                    _search,
                    filters
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelHDProductDetails");
                return StatusCode(500, "An error occurred while selecting HD product details");
            }
        }
        #endregion
        #region ::: GetScheduledDropins :::
        /// <summary>
        /// Get Scheduled Dropins
        /// </summary>
        /// <param name="request">Get scheduled dropins request</param>
        /// <returns>Scheduled dropins data</returns>
        [HttpPost("get-scheduled-dropins")]
        public IActionResult GetScheduledDropins([FromBody] GetScheduledDropinsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-scheduled-dropins");

                var result = _helpDeskServiceRequestServices.GetScheduledDropins(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetScheduledDropins");
                return StatusCode(500, "An error occurred while getting scheduled dropins");
            }
        }
        #endregion

        #region ::: SelectAllDropdownData :::
        /// <summary>
        /// Select All Dropdown Data
        /// </summary>
        /// <param name="request">Select all dropdown data request</param>
        /// <returns>Dropdown data</returns>
        [HttpPost("select-all-dropdown-data")]
        public IActionResult SelectAllDropdownData([FromBody] SelectAllDropdownDataList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-all-dropdown-data");

                var result = _helpDeskServiceRequestServices.SelectAllDropdownData(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectAllDropdownData");
                return StatusCode(500, "An error occurred while selecting all dropdown data");
            }
        }
        #endregion

        #region ::: SelectFieldSearchName :::
        /// <summary>
        /// Select Field Search Name
        /// </summary>
        /// <param name="request">Select field search name request</param>
        /// <returns>Field search name data</returns>
        [HttpPost("select-field-search-name")]
        public async Task<IActionResult> SelectFieldSearchName([FromBody] SelectFieldSearchNameList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "", [FromQuery] bool advnce = false, [FromQuery] string Query = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-field-search-name");

                var result = await _helpDeskServiceRequestServices.SelectFieldSearchName(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows,
                    _search,
                    filters,
                    advnce,
                    Query
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchName");
                return StatusCode(500, "An error occurred while selecting field search name");
            }
        }
        #endregion

        #region ::: GetContactPersonDetails :::
        /// <summary>
        /// Get Contact Person Details
        /// </summary>
        /// <param name="request">Get contact person details request</param>
        /// <returns>Contact person details</returns>
        [HttpPost("get-contact-person-details")]
        public IActionResult GetContactPersonDetails([FromBody] GetContactPersonDetailsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-contact-person-details");

                var result = _helpDeskServiceRequestServices.GetContactPersonDetails(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetContactPersonDetails");
                return StatusCode(500, "An error occurred while getting contact person details");
            }
        }
        #endregion

        #region ::: SelectMultiplePartPrefix :::
        /// <summary>
        /// Select Multiple Part Prefix
        /// </summary>
        /// <param name="request">Select multiple part prefix request</param>
        /// <returns>Multiple part prefix data</returns>
        [HttpPost("select-multiple-part-prefix")]
        public IActionResult SelectMultiplePartPrefix([FromBody] SelectMultiplePartPrefixList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-multiple-part-prefix");

                var result = _helpDeskServiceRequestServices.SelectMultiplePartPrefix(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectMultiplePartPrefix");
                return StatusCode(500, "An error occurred while selecting multiple part prefix");
            }
        }
        #endregion

        #region ::: CheckRequestPart :::
        /// <summary>
        /// Check Request Part
        /// </summary>
        /// <param name="request">Check request part request</param>
        /// <returns>Request part check result</returns>
        [HttpPost("check-request-part")]
        public IActionResult CheckRequestPart([FromBody] CheckRequestPartList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/check-request-part");

                var result = _helpDeskServiceRequestServices.CheckRequestPart(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in CheckRequestPart");
                return StatusCode(500, "An error occurred while checking request part");
            }
        }
        #endregion
        #region ::: SelectFieldSearchPart :::
        /// <summary>
        /// Select Field Search Part
        /// </summary>
        /// <param name="request">Select field search part request</param>
        /// <returns>Field search part data</returns>
        [HttpPost("select-field-search-part")]
        public IActionResult SelectFieldSearchPart([FromBody] SelectFieldSearchPartList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "", [FromQuery] bool advnce = false, [FromQuery] string Query = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-field-search-part");

                var result = _helpDeskServiceRequestServices.SelectFieldSearchPart(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows,
                    _search,
                    filters,
                    advnce,
                    Query
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchPart");
                return StatusCode(500, "An error occurred while selecting field search part");
            }
        }
        #endregion

        #region ::: GetSecondarySegment :::
        /// <summary>
        /// Get Secondary Segment
        /// </summary>
        /// <param name="request">Get secondary segment request</param>
        /// <returns>Secondary segment data</returns>
        [HttpPost("get-secondary-segment")]
        public IActionResult GetSecondarySegment([FromBody] GetSecondarySegmentList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-secondary-segment");

                var result = _helpDeskServiceRequestServices.GetSecondarySegment(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSecondarySegment");
                return StatusCode(500, "An error occurred while getting secondary segment");
            }
        }
        #endregion

        #region ::: GetPrimarySegment_LostSalesReasons :::
        /// <summary>
        /// Get Primary Segment Lost Sales Reasons
        /// </summary>
        /// <param name="request">Get primary segment lost sales reasons request</param>
        /// <returns>Primary segment lost sales reasons data</returns>
        [HttpPost("get-primary-segment-lost-sales-reasons")]
        public IActionResult GetPrimarySegment_LostSalesReasons([FromBody] GetPrimarySegment_LostSalesReasonsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-primary-segment-lost-sales-reasons");

                var result = _helpDeskServiceRequestServices.GetPrimarySegment_LostSalesReasons(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPrimarySegment_LostSalesReasons");
                return StatusCode(500, "An error occurred while getting primary segment lost sales reasons");
            }
        }
        #endregion

        #region ::: SelectFieldSearchSerialNumber :::
        /// <summary>
        /// Select Field Search Serial Number
        /// </summary>
        /// <param name="request">Select field search serial number request</param>
        /// <returns>Field search serial number data</returns>
        [HttpPost("select-field-search-serial-number")]
        public IActionResult SelectFieldSearchSerialNumber([FromBody] SelectFieldSearchSerialNumberList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "", [FromQuery] bool advnce = false, [FromQuery] string Query = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-field-search-serial-number");

                var result = _helpDeskServiceRequestServices.SelectFieldSearchSerialNumber(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows,
                    _search,
                    filters,
                    advnce,
                    Query
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchSerialNumber");
                return StatusCode(500, "An error occurred while selecting field search serial number");
            }
        }
        #endregion

        #region ::: GetAllocationDetails :::
        /// <summary>
        /// Get Allocation Details
        /// </summary>
        /// <param name="request">Get allocation details request</param>
        /// <returns>Allocation details</returns>
        [HttpPost("get-allocation-details")]
        public IActionResult GetAllocationDetails([FromBody] GetAllocationDetailsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-allocation-details");

                var result = _helpDeskServiceRequestServices.GetAllocationDetails(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAllocationDetails");
                return StatusCode(500, "An error occurred while getting allocation details");
            }
        }
        #endregion

        #region ::: GetProductDetailsOnSerial :::
        /// <summary>
        /// Get Product Details On Serial
        /// </summary>
        /// <param name="request">Get product details on serial request</param>
        /// <returns>Product details on serial</returns>
        [HttpPost("get-product-details-on-serial")]
        public IActionResult GetProductDetailsOnSerial([FromBody] GetProductDetailsOnSerialList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-details-on-serial");

                var result = _helpDeskServiceRequestServices.GetProductDetailsOnSerial(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductDetailsOnSerial");
                return StatusCode(500, "An error occurred while getting product details on serial");
            }
        }
        #endregion
        #region ::: GetEndStep :::
        /// <summary>
        /// Get End Step
        /// </summary>
        /// <param name="request">Get end step request</param>
        /// <returns>End step data</returns>
        [HttpPost("get-end-step")]
        public IActionResult GetEndStep([FromBody] GetEndStepList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string helpDesk = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-end-step");

                var result = _helpDeskServiceRequestServices.GetEndStep(
                    request,
                    connectionString,
                    logException,
                    helpDesk
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetEndStep");
                return StatusCode(500, "An error occurred while getting end step");
            }
        }
        #endregion

        #region ::: GetProductIDOnModelBrandProductType :::
        /// <summary>
        /// Get Product ID On Model Brand Product Type
        /// </summary>
        /// <param name="request">Get product ID on model brand product type request</param>
        /// <returns>Product ID data</returns>
        [HttpPost("get-product-id-on-model-brand-product-type")]
        public IActionResult GetProductIDOnModelBrandProductType([FromBody] GetProductIDOnModelBrandProductTypeList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-product-id-on-model-brand-product-type");

                var result = _helpDeskServiceRequestServices.GetProductIDOnModelBrandProductType(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetProductIDOnModelBrandProductType");
                return StatusCode(500, "An error occurred while getting product ID on model brand product type");
            }
        }
        #endregion

        #region ::: UpdatePartyContactDetails :::
        /// <summary>
        /// Update Party Contact Details
        /// </summary>
        /// <param name="request">Update party contact details request</param>
        /// <returns>Update result</returns>
        [HttpPost("update-party-contact-details")]
        public IActionResult UpdatePartyContactDetails([FromBody] UpdatePartyContactDetailsList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/update-party-contact-details");

                var result = _helpDeskServiceRequestServices.UpdatePartyContactDetails(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdatePartyContactDetails");
                return StatusCode(500, "An error occurred while updating party contact details");
            }
        }
        #endregion

        #region ::: GetDepartmentandNameData :::
        /// <summary>
        /// Get Department and Name Data
        /// </summary>
        /// <param name="request">Get department and name data request</param>
        /// <returns>Department and name data</returns>
        [HttpPost("get-department-and-name-data")]
        public IActionResult GetDepartmentandNameData([FromBody] GetDepartmentandNameDataList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-department-and-name-data");

                var result = _helpDeskServiceRequestServices.GetDepartmentandNameData(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetDepartmentandNameData");
                return StatusCode(500, "An error occurred while getting department and name data");
            }
        }
        #endregion

        #region ::: SelServiceRequestNavigation :::
        /// <summary>
        /// Select Service Request Navigation
        /// </summary>
        /// <param name="request">Select service request navigation request</param>
        /// <returns>Service request navigation data</returns>
        [HttpPost("sel-service-request-navigation")]
        public IActionResult SelServiceRequestNavigation([FromBody] SelServiceRequestNavigationList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string helpDesk = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/sel-service-request-navigation");

                var result = _helpDeskServiceRequestServices.SelServiceRequestNavigation(
                    request,
                    connectionString,
                    logException,
                    helpDesk
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelServiceRequestNavigation");
                return StatusCode(500, "An error occurred while selecting service request navigation");
            }
        }
        #endregion

        #region ::: Insert :::
        /// <summary>
        /// Insert Service Request
        /// </summary>
        /// <param name="request">Insert service request</param>
        /// <returns>Insert result</returns>
        [HttpPost("insert")]
        public IActionResult Insert([FromBody] ServiceRequest_SelectList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/insert");

                var result = _helpDeskServiceRequestServices.Insert(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Insert");
                return StatusCode(500, "An error occurred while inserting service request");
            }
        }
        #endregion

        #region ::: Edit :::
        /// <summary>
        /// Edit Service Request
        /// </summary>
        /// <param name="request">Edit service request</param>
        /// <returns>Edit result</returns>
        [HttpPost("edit")]
        public IActionResult Edit([FromBody] ServiceRequest_SelectList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/edit");

                var result = _helpDeskServiceRequestServices.Edit(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Edit");
                return StatusCode(500, "An error occurred while editing service request");
            }
        }
        #endregion
        #region ::: UpdateDesc :::
        /// <summary>
        /// Update Description
        /// </summary>
        /// <param name="request">Update description request</param>
        /// <returns>Update result</returns>
        [HttpPost("update-desc")]
        public IActionResult UpdateDesc([FromBody] UpdateDescList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/update-desc");

                var result = _helpDeskServiceRequestServices.UpdateDesc(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UpdateDesc");
                return StatusCode(500, "An error occurred while updating description");
            }
        }
        #endregion

        #region ::: getSerialNumberForModel :::
        /// <summary>
        /// Get Serial Number For Model
        /// </summary>
        /// <param name="request">Get serial number for model request</param>
        /// <returns>Serial number data</returns>
        [HttpPost("get-serial-number-for-model")]
        public IActionResult GetSerialNumberForModel([FromBody] getSerialNumberForModelFList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/get-serial-number-for-model");

                var result = _helpDeskServiceRequestServices.getSerialNumberForModel(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetSerialNumberForModel");
                return StatusCode(500, "An error occurred while getting serial number for model");
            }
        }
        #endregion

        #region ::: ProductMasterSave :::
        /// <summary>
        /// Product Master Save
        /// </summary>
        /// <param name="request">Product master save request</param>
        /// <returns>Save result</returns>
        [HttpPost("product-master-save")]
        public IActionResult ProductMasterSave([FromBody] ProductMasterSaveList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/product-master-save");

                var result = _helpDeskServiceRequestServices.ProductMasterSave(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ProductMasterSave");
                return StatusCode(500, "An error occurred while saving product master");
            }
        }
        #endregion

        #region ::: ProductMasterSaveConfirm :::
        /// <summary>
        /// Product Master Save Confirm
        /// </summary>
        /// <param name="request">Product master save confirm request</param>
        /// <returns>Save confirm result</returns>
        [HttpPost("product-master-save-confirm")]
        public IActionResult ProductMasterSaveConfirm([FromBody] ProductMasterSaveConfirmFList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/product-master-save-confirm");

                var result = _helpDeskServiceRequestServices.ProductMasterSaveConfirm(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ProductMasterSaveConfirm");
                return StatusCode(500, "An error occurred while confirming product master save");
            }
        }
        #endregion

        #region ::: SelectModel :::
        /// <summary>
        /// Select Model
        /// </summary>
        /// <param name="request">Select model request</param>
        /// <returns>Model data</returns>
        [HttpPost("select-model")]
        public IActionResult SelectModel([FromBody] HD_SelectModelList request, [FromQuery] string connectionString, [FromQuery] int logException = 1)
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-model");

                var result = _helpDeskServiceRequestServices.SelectModel(
                    request,
                    connectionString,
                    logException
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectModel");
                return StatusCode(500, "An error occurred while selecting model");
            }
        }
        #endregion

        #region ::: SelectFieldSearchModel :::
        /// <summary>
        /// Select Field Search Model
        /// </summary>
        /// <param name="request">Select field search model request</param>
        /// <returns>Field search model data</returns>
        [HttpPost("select-field-search-model")]
        public IActionResult SelectFieldSearchModel([FromBody] SelectFieldSearchModelList request, [FromQuery] string connectionString, [FromQuery] int logException = 1, [FromQuery] string sidx = "", [FromQuery] string sord = "", [FromQuery] int page = 1, [FromQuery] int rows = 10, [FromQuery] bool _search = false, [FromQuery] string filters = "")
        {
            try
            {
                _logger.LogInformation("POST /api/HelpDeskServiceRequest/select-field-search-model");

                var result = _helpDeskServiceRequestServices.SelectFieldSearchModel(
                    request,
                    connectionString,
                    logException,
                    sidx,
                    sord,
                    page,
                    rows,
                    _search,
                    filters
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SelectFieldSearchModel");
                return StatusCode(500, "An error occurred while selecting field search model");
            }
        }
        #endregion





    }
}
