using Microsoft.AspNetCore.Mvc;
using PBC.UtilityService.Utilities.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Resources;

namespace PBC.UtilityService.Utilities.Models
{
    public class SelAllAttachmentList
    {
        public int TransactionID { get; set; }
        public int DetailID { get; set; }
        public int LangID { get; set; }
        public string ObjectNames { get; set; }
        public string Tablename { get; set; }
        public string UserCulture { get; set; }
        public string AttachmentData { get; set; }
        public string AppPathString { get; set; }
        public int UserLanguageID { get; set; }
        public int GeneralLanguageID { get; set; }
    }
}